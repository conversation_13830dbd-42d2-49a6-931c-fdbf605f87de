# 预测性维护服务 Docker Compose 配置

version: '3.8'

services:
  # 预测性维护服务
  predictive-maintenance-service:
    build:
      context: .
      target: development
    container_name: predictive-maintenance-service
    ports:
      - "3020:3020"
      - "3021:3021"
    environment:
      - NODE_ENV=development
      - PORT=3020
      - MICROSERVICE_PORT=3021
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=password
      - DB_DATABASE=predictive_maintenance
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
      - ./models:/app/models
      - ./uploads:/app/uploads
    depends_on:
      - mysql
      - redis
    networks:
      - predictive-maintenance-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3020/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: predictive-maintenance-mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=predictive_maintenance
      - MYSQL_USER=app_user
      - MYSQL_PASSWORD=app_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - predictive-maintenance-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: predictive-maintenance-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - predictive-maintenance-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: predictive-maintenance-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - predictive-maintenance-service
    networks:
      - predictive-maintenance-network
    restart: unless-stopped

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: predictive-maintenance-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - predictive-maintenance-network
    restart: unless-stopped

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: predictive-maintenance-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - predictive-maintenance-network
    restart: unless-stopped

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  predictive-maintenance-network:
    driver: bridge
