/**
 * 设备健康记录实体
 */

import { 
  Entity, 
  PrimaryGeneratedColumn, 
  Column, 
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index
} from 'typeorm';
import { DeviceEntity } from './device.entity';

@Entity('device_health_records')
@Index(['deviceId', 'timestamp'])
@Index(['timestamp'])
@Index(['healthStatus'])
export class DeviceHealthRecord {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'device_id', length: 50 })
  deviceId: string;

  @Column({ type: 'datetime' })
  timestamp: Date;

  @Column({ name: 'overall_score', type: 'decimal', precision: 5, scale: 2 })
  overallScore: number;

  @Column({ 
    name: 'health_status',
    type: 'enum', 
    enum: ['excellent', 'good', 'fair', 'poor', 'critical']
  })
  healthStatus: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';

  @Column({ name: 'mechanical_score', type: 'decimal', precision: 5, scale: 2 })
  mechanicalScore: number;

  @Column({ name: 'electrical_score', type: 'decimal', precision: 5, scale: 2 })
  electricalScore: number;

  @Column({ name: 'thermal_score', type: 'decimal', precision: 5, scale: 2 })
  thermalScore: number;

  @Column({ name: 'vibration_score', type: 'decimal', precision: 5, scale: 2 })
  vibrationScore: number;

  @Column({ name: 'performance_score', type: 'decimal', precision: 5, scale: 2 })
  performanceScore: number;

  @Column({ name: 'short_term_trend', type: 'enum', enum: ['improving', 'stable', 'degrading'] })
  shortTermTrend: 'improving' | 'stable' | 'degrading';

  @Column({ name: 'long_term_trend', type: 'enum', enum: ['improving', 'stable', 'degrading'] })
  longTermTrend: 'improving' | 'stable' | 'degrading';

  // 传感器数据
  @Column({ type: 'decimal', precision: 8, scale: 3 })
  temperature: number;

  @Column({ type: 'decimal', precision: 8, scale: 3 })
  vibration: number;

  @Column({ type: 'decimal', precision: 8, scale: 3 })
  pressure: number;

  @Column({ type: 'decimal', precision: 8, scale: 3 })
  current: number;

  @Column({ type: 'decimal', precision: 8, scale: 3 })
  voltage: number;

  @Column({ type: 'decimal', precision: 8, scale: 3 })
  speed: number;

  @Column({ type: 'decimal', precision: 8, scale: 3 })
  torque: number;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  efficiency: number;

  @Column({ name: 'operating_hours', type: 'decimal', precision: 10, scale: 2 })
  operatingHours: number;

  @Column({ name: 'cycle_count', type: 'bigint' })
  cycleCount: number;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => DeviceEntity, device => device.healthRecords)
  @JoinColumn({ name: 'device_id', referencedColumnName: 'deviceId' })
  device: DeviceEntity;
}
