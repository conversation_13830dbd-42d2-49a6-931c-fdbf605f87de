/**
 * Docker健康检查脚本
 */

const http = require('http');

const options = {
  hostname: 'localhost',
  port: process.env.PORT || 3060,
  path: '/health',
  method: 'GET',
  timeout: 3000,
};

const req = http.request(options, (res) => {
  if (res.statusCode === 200) {
    process.exit(0);
  } else {
    console.error(`健康检查失败，状态码: ${res.statusCode}`);
    process.exit(1);
  }
});

req.on('error', (err) => {
  console.error('健康检查请求失败:', err.message);
  process.exit(1);
});

req.on('timeout', () => {
  console.error('健康检查超时');
  req.destroy();
  process.exit(1);
});

req.end();
