/**
 * 创建场景实体DTO
 */
import { IsString, IsNotEmpty, IsOptional, IsObject, ValidateNested, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

class TransformDto {
  @ApiProperty({ description: '位置', example: [0, 0, 0] })
  @IsArray()
  position: [number, number, number];

  @ApiProperty({ description: '旋转', example: [0, 0, 0] })
  @IsArray()
  rotation: [number, number, number];

  @ApiProperty({ description: '缩放', example: [1, 1, 1] })
  @IsArray()
  scale: [number, number, number];
}

export class CreateSceneEntityDto {
  @ApiProperty({ description: '实体名称', example: '立方体' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: '实体类型', example: 'Mesh' })
  @IsString()
  @IsNotEmpty()
  type: string;

  @ApiProperty({ description: '变换', type: TransformDto, required: false })
  @ValidateNested()
  @Type(() => TransformDto)
  @IsOptional()
  transform?: TransformDto;

  @ApiProperty({ description: '实体属性', required: false, type: 'object' })
  @IsObject()
  @IsOptional()
  properties?: Record<string, any>;

  @ApiProperty({ description: '父实体ID', required: false })
  @IsString()
  @IsOptional()
  parentId?: string;
}
