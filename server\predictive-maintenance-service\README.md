# 预测性维护服务 (Predictive Maintenance Service)

基于AI的工业设备预测性维护微服务，提供设备健康状态分析、故障预测、维护建议生成等功能。

## 🚀 功能特性

### 核心功能
- **设备健康状态分析** - 基于多维传感器数据的实时健康评估
- **故障预测** - 使用深度学习模型预测设备故障风险
- **维护建议生成** - 智能生成个性化维护计划和建议
- **剩余使用寿命计算** - 基于退化模型的RUL预测
- **异常检测** - 实时检测设备运行异常
- **AI模型管理** - 支持模型训练、更新和版本管理

### 技术特性
- **微服务架构** - 基于NestJS的模块化设计
- **AI/ML集成** - TensorFlow.js深度学习模型
- **实时监控** - WebSocket实时数据推送
- **高性能缓存** - Redis缓存优化
- **健康检查** - 多层次服务健康监控
- **API文档** - Swagger自动生成文档

## 📋 系统要求

- Node.js >= 18.0.0
- MySQL >= 8.0
- Redis >= 6.0
- Docker >= 20.10 (可选)
- Docker Compose >= 2.0 (可选)

## 🛠️ 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd server/predictive-maintenance-service

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库和Redis连接信息
```

### 2. 数据库设置

```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE predictive_maintenance;

# 运行数据库迁移（如果有）
npm run migration:run
```

### 3. 启动服务

```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod

# 调试模式
npm run start:debug
```

### 4. 使用Docker

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f predictive-maintenance-service

# 停止服务
docker-compose down
```

## 📖 API文档

服务启动后，访问以下地址查看API文档：

- **Swagger文档**: http://localhost:3020/api/docs
- **健康检查**: http://localhost:3020/api/v1/health

### 主要API端点

#### 设备健康分析
```http
POST /api/v1/maintenance/analyze-health
Content-Type: application/json

{
  "deviceId": "device-001",
  "timeRange": {
    "start": "2024-01-01T00:00:00.000Z",
    "end": "2024-01-07T23:59:59.999Z"
  }
}
```

#### 故障预测
```http
POST /api/v1/maintenance/predict-failures
Content-Type: application/json

{
  "deviceId": "device-001",
  "predictionHorizon": 168
}
```

#### 维护建议生成
```http
POST /api/v1/maintenance/generate-recommendations
Content-Type: application/json

{
  "deviceId": "device-001",
  "includePreventive": true
}
```

## 🏗️ 项目结构

```
src/
├── app.module.ts              # 主应用模块
├── main.ts                    # 应用启动入口
├── maintenance/               # 维护业务模块
│   ├── dto/                   # 数据传输对象
│   ├── entities/              # 数据库实体
│   ├── maintenance.controller.ts
│   ├── maintenance.service.ts
│   └── maintenance.module.ts
├── health/                    # 健康检查模块
│   ├── health.controller.ts
│   ├── health.service.ts
│   └── health.module.ts
├── common/                    # 公共组件
│   ├── filters/               # 异常过滤器
│   └── interceptors/          # 拦截器
└── config/                    # 配置文件
```

## 🔧 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `NODE_ENV` | 运行环境 | `development` |
| `PORT` | HTTP服务端口 | `3020` |
| `MICROSERVICE_PORT` | 微服务端口 | `3021` |
| `DB_HOST` | 数据库主机 | `localhost` |
| `DB_PORT` | 数据库端口 | `3306` |
| `DB_USERNAME` | 数据库用户名 | `root` |
| `DB_PASSWORD` | 数据库密码 | `` |
| `DB_DATABASE` | 数据库名称 | `predictive_maintenance` |
| `REDIS_HOST` | Redis主机 | `localhost` |
| `REDIS_PORT` | Redis端口 | `6379` |

### AI模型配置

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `AI_MODEL_PATH` | 模型文件路径 | `./models` |
| `TENSORFLOW_BACKEND` | TensorFlow后端 | `cpu` |
| `MODEL_BATCH_SIZE` | 模型批处理大小 | `32` |
| `MODEL_PREDICTION_THRESHOLD` | 预测阈值 | `0.7` |

## 🧪 测试

```bash
# 单元测试
npm run test

# 端到端测试
npm run test:e2e

# 测试覆盖率
npm run test:cov

# 监听模式测试
npm run test:watch
```

## 📊 监控

### 健康检查端点

- `/api/v1/health` - 综合健康检查
- `/api/v1/health/database` - 数据库连接检查
- `/api/v1/health/redis` - Redis连接检查
- `/api/v1/health/ai-models` - AI模型状态检查
- `/api/v1/health/devices` - 设备连接检查

### 监控指标

服务提供以下监控指标：

- 请求响应时间
- 错误率统计
- 内存使用情况
- CPU使用率
- 数据库连接状态
- Redis连接状态
- AI模型性能指标

## 🚀 部署

### Docker部署

```bash
# 构建镜像
docker build -t predictive-maintenance-service .

# 运行容器
docker run -d \
  --name predictive-maintenance-service \
  -p 3020:3020 \
  -p 3021:3021 \
  -e NODE_ENV=production \
  -e DB_HOST=your-db-host \
  -e REDIS_HOST=your-redis-host \
  predictive-maintenance-service
```

### Kubernetes部署

```bash
# 应用Kubernetes配置
kubectl apply -f k8s/

# 查看部署状态
kubectl get pods -l app=predictive-maintenance-service

# 查看服务日志
kubectl logs -f deployment/predictive-maintenance-service
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 创建 [Issue](../../issues)
- 发送邮件至: <EMAIL>
- 查看 [Wiki](../../wiki) 获取更多文档

## 🔄 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础预测性维护功能
- AI模型集成
- 健康检查系统
- API文档生成

## 🎯 AI模型说明

### 故障预测模型
- **模型类型**: 深度神经网络
- **输入特征**: 12维传感器数据（温度、振动、压力等）
- **输出**: 8种故障类型的概率预测
- **准确率**: 92%+

### 异常检测模型
- **模型类型**: 自编码器
- **检测方法**: 重构误差阈值
- **实时性**: 毫秒级响应
- **误报率**: <5%

### RUL预测模型
- **模型类型**: LSTM循环神经网络
- **时间序列长度**: 10个时间步
- **预测精度**: ±10%
- **适用场景**: 渐进式退化设备
