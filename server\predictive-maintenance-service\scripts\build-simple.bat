@echo off
echo 开始简化构建预测性维护服务...

REM 清理之前的构建
if exist dist (
    echo 清理之前的构建文件...
    rmdir /s /q dist
)

REM 检查依赖是否安装
if not exist node_modules (
    echo 依赖未安装，正在安装基础依赖...
    npm install --omit=optional --legacy-peer-deps --no-audit --no-fund
)

REM 执行构建
echo 正在编译 TypeScript...
npx tsc

if %ERRORLEVEL% EQU 0 (
    echo 构建成功！
    echo 构建文件位于 dist/ 目录
    echo.
    echo 注意：此版本暂时禁用了以下功能以确保构建成功：
    echo - 微服务支持
    echo - Redis缓存
    echo 这些功能可以在依赖问题解决后重新启用
) else (
    echo 构建失败，请检查错误信息
    exit /b 1
)

pause
