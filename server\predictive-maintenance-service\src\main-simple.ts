/**
 * 简化版启动文件 - 用于测试
 */

import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';

// 创建一个最简单的模块
import { Mo<PERSON><PERSON>, Controller, Get } from '@nestjs/common';

@Controller()
class TestController {
  @Get()
  getHello(): string {
    return 'Hello World! 预测性维护服务正在运行';
  }

  @Get('health')
  getHealth(): object {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: '预测性维护服务',
      version: '1.0.0'
    };
  }
}

@Module({
  controllers: [TestController],
})
class TestModule {}

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  try {
    logger.log('开始启动简化版服务...');
    
    // 创建应用实例
    const app = await NestFactory.create(TestModule, {
      logger: ['error', 'warn', 'log'],
    });

    // 设置全局前缀
    app.setGlobalPrefix('api/v1');

    // 启用CORS
    app.enableCors({
      origin: '*',
      credentials: true,
    });

    // 启动HTTP服务
    const port = 3020;
    const host = '0.0.0.0';

    await app.listen(port, host);

    logger.log(`🚀 简化版预测性维护服务已启动`);
    logger.log(`📍 HTTP服务地址: http://${host}:${port}`);
    logger.log(`🔍 健康检查: http://${host}:${port}/api/v1/health`);

  } catch (error) {
    logger.error('服务启动失败:', error);
    process.exit(1);
  }
}

bootstrap();
