# 感知数据处理服务 Docker Compose 配置

version: '3.8'

services:
  # 感知数据处理服务
  perception-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: perception-service
    ports:
      - "3050:3050"
    environment:
      - NODE_ENV=development
      - PORT=3050
      - HOST=0.0.0.0
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
    depends_on:
      - redis
    networks:
      - perception-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3050/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis服务
  redis:
    image: redis:7-alpine
    container_name: perception-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - perception-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Commander (可选的Redis管理界面)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: perception-redis-commander
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - perception-network
    restart: unless-stopped
    profiles:
      - tools

volumes:
  redis-data:
    driver: local

networks:
  perception-network:
    driver: bridge
