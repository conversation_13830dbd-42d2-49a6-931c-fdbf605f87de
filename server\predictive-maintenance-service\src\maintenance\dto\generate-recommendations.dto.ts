/**
 * 生成维护建议DTO
 */

import { IsString, IsOptional, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class GenerateRecommendationsDto {
  @ApiProperty({ 
    description: '设备ID', 
    example: 'device-001',
    minLength: 1,
    maxLength: 50
  })
  @IsString()
  deviceId: string;

  @ApiPropertyOptional({ 
    description: '是否包含预防性维护建议', 
    example: true,
    default: true
  })
  @IsOptional()
  @IsBoolean()
  includePreventive?: boolean = true;
}
