/**
 * 预测性维护控制器
 */

import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Param, 
  Query, 
  UseGuards,
  HttpStatus,
  HttpCode
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery,
  ApiBearerAuth 
} from '@nestjs/swagger';

import {
  MaintenanceService,
  HealthScore,
  FailurePrediction,
  MaintenanceRecommendation
} from './maintenance-simple.service';
import { AnalyzeHealthDto } from './dto/analyze-health.dto';
import { PredictFailuresDto } from './dto/predict-failures.dto';
import { GenerateRecommendationsDto } from './dto/generate-recommendations.dto';
import { CalculateRulDto } from './dto/calculate-rul.dto';
import { DetectAnomaliesDto } from './dto/detect-anomalies.dto';
import { HealthScoreResponseDto } from './dto/health-score-response.dto';
import { FailurePredictionResponseDto } from './dto/failure-prediction-response.dto';
import { MaintenanceRecommendationResponseDto } from './dto/maintenance-recommendation-response.dto';

@ApiTags('maintenance')
@ApiBearerAuth()
@Controller('maintenance')
export class MaintenanceController {
  constructor(private readonly maintenanceService: MaintenanceService) {}

  @Post('analyze-health')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: '分析设备健康状态',
    description: '基于设备传感器数据分析设备的整体健康状态和各组件评分'
  })
  @ApiResponse({ 
    status: 200, 
    description: '健康状态分析成功',
    type: HealthScoreResponseDto
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '设备不存在或无数据' })
  async analyzeDeviceHealth(@Body() analyzeHealthDto: AnalyzeHealthDto): Promise<HealthScore> {
    const { deviceId, timeRange } = analyzeHealthDto;
    // 创建模拟数据用于测试
    const mockData = {
      deviceId,
      timestamp: new Date(),
      temperature: 65,
      vibration: 2.5,
      pressure: 5.0,
      humidity: 45,
      voltage: 220,
      current: 15,
      speed: 1500,
      torque: 500,
      efficiency: 85,
      errorCount: 0,
      operatingHours: 1000,
      maintenanceHistory: 5
    };
    return await this.maintenanceService.analyzeDeviceHealth(mockData);
  }

  @Post('predict-failures')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: '预测设备故障',
    description: '使用AI模型预测设备在指定时间范围内的故障风险'
  })
  @ApiResponse({ 
    status: 200, 
    description: '故障预测成功',
    type: [FailurePredictionResponseDto]
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '设备不存在或数据不足' })
  async predictFailures(@Body() predictFailuresDto: PredictFailuresDto): Promise<FailurePrediction[]> {
    const { deviceId, predictionHorizon } = predictFailuresDto;
    // 创建模拟数据用于测试
    const mockData = {
      deviceId,
      timestamp: new Date(),
      temperature: 75,
      vibration: 3.5,
      pressure: 6.0,
      humidity: 50,
      voltage: 215,
      current: 18,
      speed: 1600,
      torque: 550,
      efficiency: 80,
      errorCount: 2,
      operatingHours: 1200,
      maintenanceHistory: 3
    };
    return await this.maintenanceService.predictFailures(mockData);
  }

  @Post('generate-recommendations')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: '生成维护建议',
    description: '基于设备健康状态和故障预测生成个性化维护建议'
  })
  @ApiResponse({ 
    status: 200, 
    description: '维护建议生成成功',
    type: [MaintenanceRecommendationResponseDto]
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '设备不存在' })
  async generateMaintenanceRecommendations(@Body() generateRecommendationsDto: GenerateRecommendationsDto): Promise<MaintenanceRecommendation[]> {
    const { deviceId, includePreventive } = generateRecommendationsDto;
    // 创建模拟数据用于测试
    const mockData = {
      deviceId,
      timestamp: new Date(),
      temperature: 70,
      vibration: 3.0,
      pressure: 5.5,
      humidity: 48,
      voltage: 225,
      current: 16,
      speed: 1550,
      torque: 520,
      efficiency: 82,
      errorCount: 1,
      operatingHours: 1100,
      maintenanceHistory: 4
    };
    return await this.maintenanceService.generateMaintenanceRecommendations(mockData);
  }

  @Post('calculate-rul')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: '计算剩余使用寿命',
    description: '基于设备退化模型计算设备或组件的剩余使用寿命'
  })
  @ApiResponse({ 
    status: 200, 
    description: '剩余使用寿命计算成功',
    schema: {
      type: 'object',
      properties: {
        deviceId: { type: 'string' },
        component: { type: 'string' },
        remainingUsefulLife: { type: 'number', description: '剩余使用寿命（小时）' },
        confidence: { type: 'number', description: '预测置信度' },
        calculatedAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '设备不存在或数据不足' })
  async calculateRemainingUsefulLife(@Body() calculateRulDto: CalculateRulDto) {
    const { deviceId, component } = calculateRulDto;
    const rul = await this.maintenanceService.calculateRemainingUsefulLife(deviceId, component);
    
    return {
      deviceId,
      component: component || 'overall',
      remainingUsefulLife: rul,
      confidence: 0.85, // 简化实现
      calculatedAt: new Date().toISOString()
    };
  }

  @Post('detect-anomalies')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: '异常检测',
    description: '检测设备运行数据中的异常模式和异常值'
  })
  @ApiResponse({ 
    status: 200, 
    description: '异常检测成功',
    schema: {
      type: 'object',
      properties: {
        deviceId: { type: 'string' },
        anomalies: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              metric: { type: 'string' },
              value: { type: 'number' },
              threshold: { type: 'number' },
              severity: { type: 'string', enum: ['low', 'medium', 'high'] },
              timestamp: { type: 'string', format: 'date-time' }
            }
          }
        },
        detectedAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '设备不存在或数据不足' })
  async detectAnomalies(@Body() detectAnomaliesDto: DetectAnomaliesDto) {
    const { deviceId, sensitivity } = detectAnomaliesDto;
    const anomalies = await this.maintenanceService.detectAnomalies(deviceId, sensitivity);
    
    return {
      deviceId,
      anomalies,
      detectedAt: new Date().toISOString()
    };
  }

  @Get('health/:deviceId')
  @ApiOperation({ 
    summary: '获取设备健康状态',
    description: '获取指定设备的最新健康状态评分'
  })
  @ApiParam({ name: 'deviceId', description: '设备ID' })
  @ApiResponse({ 
    status: 200, 
    description: '健康状态获取成功',
    type: HealthScoreResponseDto
  })
  @ApiResponse({ status: 404, description: '设备不存在' })
  async getDeviceHealth(@Param('deviceId') deviceId: string): Promise<HealthScore> {
    return await this.maintenanceService.analyzeDeviceHealth(deviceId);
  }

  @Get('predictions/:deviceId')
  @ApiOperation({ 
    summary: '获取设备故障预测',
    description: '获取指定设备的故障预测结果'
  })
  @ApiParam({ name: 'deviceId', description: '设备ID' })
  @ApiQuery({ name: 'horizon', description: '预测时间范围（小时）', required: false })
  @ApiResponse({ 
    status: 200, 
    description: '故障预测获取成功',
    type: [FailurePredictionResponseDto]
  })
  @ApiResponse({ status: 404, description: '设备不存在' })
  async getFailurePredictions(
    @Param('deviceId') deviceId: string,
    @Query('horizon') horizon?: number
  ): Promise<FailurePrediction[]> {
    return await this.maintenanceService.predictFailures(deviceId, horizon);
  }

  @Get('recommendations/:deviceId')
  @ApiOperation({ 
    summary: '获取维护建议',
    description: '获取指定设备的维护建议'
  })
  @ApiParam({ name: 'deviceId', description: '设备ID' })
  @ApiQuery({ name: 'preventive', description: '是否包含预防性维护', required: false })
  @ApiResponse({ 
    status: 200, 
    description: '维护建议获取成功',
    type: [MaintenanceRecommendationResponseDto]
  })
  @ApiResponse({ status: 404, description: '设备不存在' })
  async getMaintenanceRecommendations(
    @Param('deviceId') deviceId: string,
    @Query('preventive') preventive?: boolean
  ): Promise<MaintenanceRecommendation[]> {
    return await this.maintenanceService.generateMaintenanceRecommendations(deviceId, preventive);
  }
}
