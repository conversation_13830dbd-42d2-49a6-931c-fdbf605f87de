/**
 * 故障预测记录实体
 */

import { 
  Entity, 
  PrimaryGeneratedColumn, 
  Column, 
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index
} from 'typeorm';
import { DeviceEntity } from './device.entity';

@Entity('failure_prediction_records')
@Index(['deviceId', 'timestamp'])
@Index(['failureType'])
@Index(['severity'])
@Index(['probability'])
export class FailurePredictionRecord {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'device_id', length: 50 })
  deviceId: string;

  @Column({ 
    name: 'failure_type',
    type: 'enum', 
    enum: ['mechanical', 'electrical', 'hydraulic', 'pneumatic', 'software', 'sensor', 'wear', 'overheating']
  })
  failureType: 'mechanical' | 'electrical' | 'hydraulic' | 'pneumatic' | 'software' | 'sensor' | 'wear' | 'overheating';

  @Column({ type: 'decimal', precision: 5, scale: 4 })
  probability: number;

  @Column({ name: 'time_to_failure', type: 'decimal', precision: 10, scale: 2 })
  timeToFailure: number;

  @Column({ type: 'decimal', precision: 5, scale: 4 })
  confidence: number;

  @Column({ 
    type: 'enum', 
    enum: ['low', 'medium', 'high']
  })
  severity: 'low' | 'medium' | 'high';

  @Column({ name: 'contributing_factors', type: 'json' })
  contributingFactors: string[];

  @Column({ name: 'recommended_actions', type: 'json' })
  recommendedActions: string[];

  @Column({ name: 'prediction_horizon', type: 'int' })
  predictionHorizon: number;

  @Column({ name: 'model_version', length: 50, nullable: true })
  modelVersion: string;

  @Column({ name: 'model_accuracy', type: 'decimal', precision: 5, scale: 4, nullable: true })
  modelAccuracy: number;

  @Column({ type: 'datetime' })
  timestamp: Date;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @Column({ name: 'resolved_at', type: 'datetime', nullable: true })
  resolvedAt: Date;

  @Column({ name: 'resolution_notes', type: 'text', nullable: true })
  resolutionNotes: string;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => DeviceEntity, device => device.failurePredictions)
  @JoinColumn({ name: 'device_id', referencedColumnName: 'deviceId' })
  device: DeviceEntity;
}
