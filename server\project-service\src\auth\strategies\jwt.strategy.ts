/**
 * JWT认证策略
 */
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { JwtService } from '@nestjs/jwt';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';

export interface JwtPayload {
  sub: string;
  username: string;
  email?: string;
  roles?: string[];
  iat?: number;
  exp?: number;
}

export interface User {
  id: string;
  username: string;
  email: string;
  roles: string[];
  isActive: boolean;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
    @Inject('USER_SERVICE') private readonly userService: ClientProxy,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET', 'your-secret-key'),
      passReqToCallback: true,
    });
  }

  async validate(req: any, payload: JwtPayload): Promise<User> {
    try {
      // 验证payload基本信息
      if (!payload.sub || !payload.username) {
        throw new UnauthorizedException('无效的token载荷');
      }

      // 从用户服务验证用户
      const user = await firstValueFrom(
        this.userService.send({ cmd: 'validateJwt' }, {
          userId: payload.sub,
          username: payload.username,
        }),
      );

      if (!user) {
        throw new UnauthorizedException('用户不存在');
      }

      if (!user.isActive) {
        throw new UnauthorizedException('用户已被禁用');
      }

      // 返回用户信息
      return {
        id: user.id,
        username: user.username,
        email: user.email,
        roles: user.roles || [],
        isActive: user.isActive,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('token验证失败');
    }
  }
}
