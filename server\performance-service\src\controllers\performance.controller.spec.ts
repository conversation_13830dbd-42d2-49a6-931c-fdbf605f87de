/**
 * 性能控制器单元测试
 */

import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { PerformanceController } from './performance.controller';
import { PerformanceOptimizationService } from '../services/performance-optimization.service';

describe('PerformanceController', () => {
  let controller: PerformanceController;
  let service: PerformanceOptimizationService;

  const mockPerformanceService = {
    getPerformanceMetrics: jest.fn(),
    generateOptimizationRecommendations: jest.fn(),
    getOptimizationConfig: jest.fn(),
    applyOptimizationConfig: jest.fn(),
    recordRequest: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PerformanceController],
      providers: [
        {
          provide: PerformanceOptimizationService,
          useValue: mockPerformanceService,
        },
      ],
    }).compile();

    controller = module.get<PerformanceController>(PerformanceController);
    service = module.get<PerformanceOptimizationService>(PerformanceOptimizationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('应该被定义', () => {
    expect(controller).toBeDefined();
  });

  describe('getPerformanceMetrics', () => {
    it('应该返回性能指标', async () => {
      const mockMetrics = [
        {
          nodeId: 'test-node',
          timestamp: Date.now(),
          cpu: { usage: 50 },
          memory: { usage: 0.6 },
        },
      ];

      mockPerformanceService.getPerformanceMetrics.mockReturnValue(mockMetrics);

      const query = { nodeId: 'test-node', limit: 10, offset: 0 };
      const result = await controller.getPerformanceMetrics(query);

      expect(result.success).toBe(true);
      expect(result.data.metrics).toEqual(mockMetrics);
      expect(service.getPerformanceMetrics).toHaveBeenCalledWith('test-node');
    });

    it('当nodeId为空时应该抛出异常', async () => {
      const query = { limit: 10, offset: 0 };

      await expect(controller.getPerformanceMetrics(query)).rejects.toThrow(
        new HttpException('节点ID不能为空', HttpStatus.BAD_REQUEST),
      );
    });
  });

  describe('getRealtimeMetrics', () => {
    it('应该返回实时性能指标', async () => {
      const mockMetrics = [
        {
          nodeId: 'test-node',
          timestamp: Date.now(),
          cpu: { usage: 50 },
          memory: { usage: 0.6 },
        },
      ];

      mockPerformanceService.getPerformanceMetrics.mockReturnValue(mockMetrics);

      const result = await controller.getRealtimeMetrics('test-node');

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockMetrics[0]);
    });

    it('当没有数据时应该抛出异常', async () => {
      mockPerformanceService.getPerformanceMetrics.mockReturnValue([]);

      await expect(controller.getRealtimeMetrics('test-node')).rejects.toThrow(
        new HttpException('未找到性能数据', HttpStatus.NOT_FOUND),
      );
    });
  });

  describe('getOptimizationRecommendations', () => {
    it('应该返回优化建议', async () => {
      const mockRecommendations = [
        {
          type: 'warning',
          category: 'memory',
          title: '内存使用率较高',
          description: '当前内存使用率超过80%',
          action: '考虑增加内存或优化内存使用',
          impact: 'medium',
          priority: 2,
          estimatedImprovement: '减少20%内存使用',
        },
      ];

      mockPerformanceService.generateOptimizationRecommendations.mockResolvedValue(
        mockRecommendations,
      );

      const result = await controller.getOptimizationRecommendations('test-node');

      expect(result.success).toBe(true);
      expect(result.data.recommendations).toEqual(mockRecommendations);
      expect(service.generateOptimizationRecommendations).toHaveBeenCalledWith('test-node');
    });
  });

  describe('updateOptimizationConfig', () => {
    it('应该更新优化配置', async () => {
      const configDto = {
        enableAutoTuning: true,
        maxMemoryUsage: 85,
        maxCpuUsage: 80,
      };

      mockPerformanceService.applyOptimizationConfig.mockResolvedValue(undefined);

      const result = await controller.updateOptimizationConfig('test-node', configDto);

      expect(result.success).toBe(true);
      expect(result.message).toBe('优化配置已更新');
      expect(service.applyOptimizationConfig).toHaveBeenCalledWith('test-node', configDto);
    });
  });

  describe('recordRequestPerformance', () => {
    it('应该记录请求性能', async () => {
      const data = { responseTime: 150, isError: false };

      mockPerformanceService.recordRequest.mockReturnValue(undefined);

      const result = await controller.recordRequestPerformance(data);

      expect(result.success).toBe(true);
      expect(result.message).toBe('性能数据已记录');
      expect(service.recordRequest).toHaveBeenCalledWith(150, false);
    });
  });

  describe('getSystemHealth', () => {
    it('应该返回系统健康状态', async () => {
      const result = await controller.getSystemHealth();

      expect(result.success).toBe(true);
      expect(result.data.status).toBe('healthy');
      expect(result.data).toHaveProperty('timestamp');
      expect(result.data).toHaveProperty('uptime');
      expect(result.data).toHaveProperty('memory');
      expect(result.data).toHaveProperty('cpu');
    });
  });
});
