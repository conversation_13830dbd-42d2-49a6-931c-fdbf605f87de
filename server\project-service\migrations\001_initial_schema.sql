-- 项目服务数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS `ir_engine_projects` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `ir_engine_projects`;

-- 创建项目表
CREATE TABLE IF NOT EXISTS `projects` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `thumbnailUrl` varchar(500),
  `visibility` enum('public','private') NOT NULL DEFAULT 'private',
  `ownerId` varchar(36) NOT NULL,
  `isTemplate` tinyint(1) NOT NULL DEFAULT '0',
  `isArchived` tinyint(1) NOT NULL DEFAULT '0',
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  KEY `idx_projects_owner` (`ownerId`),
  <PERSON><PERSON>Y `idx_projects_visibility` (`visibility`),
  KEY `idx_projects_template` (`isTemplate`),
  KEY `idx_projects_archived` (`isArchived`),
  KEY `idx_projects_created` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建项目成员表
CREATE TABLE IF NOT EXISTS `project_members` (
  `id` varchar(36) NOT NULL,
  `userId` varchar(36) NOT NULL,
  `projectId` varchar(36) NOT NULL,
  `role` enum('owner','admin','editor','viewer') NOT NULL DEFAULT 'viewer',
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_member` (`projectId`, `userId`),
  KEY `idx_project_members_user` (`userId`),
  KEY `idx_project_members_project` (`projectId`),
  KEY `idx_project_members_role` (`role`),
  CONSTRAINT `fk_project_members_project` FOREIGN KEY (`projectId`) REFERENCES `projects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建项目设置表
CREATE TABLE IF NOT EXISTS `project_settings` (
  `id` varchar(36) NOT NULL,
  `projectId` varchar(36) NOT NULL,
  `key` varchar(255) NOT NULL,
  `value` text,
  `type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string',
  `description` varchar(500),
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_setting` (`projectId`, `key`),
  KEY `idx_project_settings_project` (`projectId`),
  KEY `idx_project_settings_key` (`key`),
  CONSTRAINT `fk_project_settings_project` FOREIGN KEY (`projectId`) REFERENCES `projects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建场景表
CREATE TABLE IF NOT EXISTS `scenes` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `thumbnailUrl` varchar(500),
  `projectId` varchar(36) NOT NULL,
  `isDefault` tinyint(1) NOT NULL DEFAULT '0',
  `metadata` json,
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  KEY `idx_scenes_project` (`projectId`),
  KEY `idx_scenes_default` (`isDefault`),
  KEY `idx_scenes_created` (`createdAt`),
  CONSTRAINT `fk_scenes_project` FOREIGN KEY (`projectId`) REFERENCES `projects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建场景实体表
CREATE TABLE IF NOT EXISTS `scene_entities` (
  `id` varchar(36) NOT NULL,
  `sceneId` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` varchar(100) NOT NULL,
  `transform` json,
  `properties` json,
  `parentId` varchar(36),
  `isActive` tinyint(1) NOT NULL DEFAULT '1',
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  KEY `idx_scene_entities_scene` (`sceneId`),
  KEY `idx_scene_entities_type` (`type`),
  KEY `idx_scene_entities_parent` (`parentId`),
  KEY `idx_scene_entities_active` (`isActive`),
  CONSTRAINT `fk_scene_entities_scene` FOREIGN KEY (`sceneId`) REFERENCES `scenes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_scene_entities_parent` FOREIGN KEY (`parentId`) REFERENCES `scene_entities` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认数据
INSERT INTO `projects` (`id`, `name`, `description`, `ownerId`, `visibility`, `isTemplate`) VALUES
('default-template-1', '基础3D场景模板', '包含基本光照和地面的3D场景模板', 'system', 'public', 1),
('default-template-2', 'VR交互模板', '支持VR交互的场景模板', 'system', 'public', 1);

-- 为默认模板创建默认场景
INSERT INTO `scenes` (`id`, `name`, `description`, `projectId`, `isDefault`) VALUES
('default-scene-1', '主场景', '默认的主场景', 'default-template-1', 1),
('default-scene-2', 'VR主场景', 'VR交互的主场景', 'default-template-2', 1);
