/**
 * 预测性维护模块
 */

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
// import { CacheModule } from '@nestjs/cache-manager';

import { MaintenanceController } from './maintenance-simple.controller';
import { MaintenanceService } from './maintenance-simple.service';
// import { DeviceHealthRecord } from './entities/device-health-record.entity';
// import { FailurePredictionRecord } from './entities/failure-prediction-record.entity';
// import { MaintenanceRecommendationRecord } from './entities/maintenance-recommendation-record.entity';
// import { DeviceEntity } from './entities/device.entity';
// import { MaintenanceTaskEntity } from './entities/maintenance-task.entity';

@Module({
  imports: [
    // 数据库实体 (暂时禁用以测试服务启动)
    // TypeOrmModule.forFeature([
    //   DeviceEntity,
    //   DeviceHealthRecord,
    //   FailurePredictionRecord,
    //   MaintenanceRecommendationRecord,
    //   MaintenanceTaskEntity,
    // ]),

    // 缓存模块 (暂时禁用，避免依赖问题)
    // CacheModule.register({
    //   ttl: 300, // 5分钟缓存
    //   max: 100, // 最大缓存数量
    // }),
  ],
  controllers: [MaintenanceController],
  providers: [MaintenanceService],
  exports: [MaintenanceService],
})
export class MaintenanceModule {}
