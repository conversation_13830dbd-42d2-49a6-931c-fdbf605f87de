@echo off
echo 正在清理项目依赖...

REM 删除 node_modules 目录
if exist node_modules (
    echo 删除 node_modules 目录...
    rmdir /s /q node_modules
)

REM 删除 package-lock.json
if exist package-lock.json (
    echo 删除 package-lock.json...
    del package-lock.json
)

REM 清理 npm 缓存
echo 清理 npm 缓存...
npm cache clean --force

REM 设置 npm 配置以避免权限问题
echo 配置 npm 设置...
npm config set fund false
npm config set audit false

REM 安装依赖
echo 安装项目依赖...
npm install --omit=optional --legacy-peer-deps

echo 依赖安装完成！
pause
