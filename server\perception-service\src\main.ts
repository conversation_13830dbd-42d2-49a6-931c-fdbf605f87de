/**
 * 感知数据处理服务启动文件
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('PerceptionService');

  try {
    // 创建Nest应用实例
    const app = await NestFactory.create(AppModule);
    const configService = app.get(ConfigService);

    // 配置微服务
    const microservicePort = configService.get<number>('PERCEPTION_SERVICE_PORT', 3050);
    const microserviceOptions: MicroserviceOptions = {
      transport: Transport.TCP,
      options: {
        host: configService.get<string>('PERCEPTION_SERVICE_HOST', 'localhost'),
        port: microservicePort,
      },
    };

    // 配置HTTP服务
    // 全局前缀
    app.setGlobalPrefix('api/v1');

    // 全局管道
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );

    // CORS配置
    app.enableCors({
      origin: configService.get<string>('CORS_ORIGIN', '*'),
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    });

    // Swagger文档配置
    if (configService.get<string>('NODE_ENV') !== 'production') {
      const config = new DocumentBuilder()
        .setTitle('感知数据处理服务 API')
        .setDescription('提供大规模感知数据的实时处理、分析和存储功能')
        .setVersion('1.0')
        .addTag('perception', '感知数据处理')
        .addTag('health', '健康检查')
        .addBearerAuth()
        .build();

      const document = SwaggerModule.createDocument(app, config);
      const swaggerPath = configService.get<string>('SWAGGER_PATH', 'api/docs');
      SwaggerModule.setup(swaggerPath, app, document, {
        swaggerOptions: {
          persistAuthorization: true,
        },
      });
    }

    // 连接微服务
    app.connectMicroservice(microserviceOptions);
    await app.startAllMicroservices();
    logger.log(`微服务已启动在端口: ${microservicePort}`);

    // 启动HTTP服务
    const port = configService.get<number>('PORT', 3050);
    const host = configService.get<string>('HOST', '0.0.0.0');

    await app.listen(port, host);

    logger.log(`🚀 感知数据处理服务已启动`);
    logger.log(`📍 HTTP服务地址: http://${host}:${port}`);
    logger.log(`📖 API文档地址: http://${host}:${port}/${configService.get<string>('SWAGGER_PATH', 'api/docs')}`);
    logger.log(`🔍 健康检查: http://${host}:${port}/api/v1/health`);
    logger.log(`🔧 环境: ${configService.get<string>('NODE_ENV', 'development')}`);

    // 优雅关闭处理
    process.on('SIGTERM', async () => {
      logger.log('收到SIGTERM信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.log('收到SIGINT信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

  } catch (error) {
    logger.error('服务启动失败:', error);
    process.exit(1);
  }
}

bootstrap();
