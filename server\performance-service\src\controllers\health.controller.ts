/**
 * 健康检查控制器
 */

import { Controller, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Public } from '../decorators/public.decorator';
import { HealthService } from '../services/health.service';

@ApiTags('健康检查')
@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(private readonly healthService: HealthService) {}

  @Get()
  @Public()
  @ApiOperation({ summary: '基础健康检查' })
  @ApiResponse({ status: 200, description: '服务健康' })
  @ApiResponse({ status: 503, description: '服务不健康' })
  async check() {
    try {
      const health = await this.healthService.check();
      return {
        success: true,
        data: health,
      };
    } catch (error) {
      this.logger.error('健康检查失败:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Get('detailed')
  @Public()
  @ApiOperation({ summary: '详细健康检查' })
  @ApiResponse({ status: 200, description: '详细健康信息' })
  async detailedCheck() {
    try {
      const health = await this.healthService.detailedCheck();
      return {
        success: true,
        data: health,
      };
    } catch (error) {
      this.logger.error('详细健康检查失败:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Get('ready')
  @Public()
  @ApiOperation({ summary: '就绪检查' })
  @ApiResponse({ status: 200, description: '服务就绪' })
  @ApiResponse({ status: 503, description: '服务未就绪' })
  async readiness() {
    try {
      const ready = await this.healthService.readinessCheck();
      return {
        success: true,
        data: { ready },
      };
    } catch (error) {
      this.logger.error('就绪检查失败:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Get('live')
  @Public()
  @ApiOperation({ summary: '存活检查' })
  @ApiResponse({ status: 200, description: '服务存活' })
  async liveness() {
    return {
      success: true,
      data: {
        status: 'alive',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      },
    };
  }
}
