version: '3.8'

services:
  # 性能优化服务
  performance-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: performance-service
    restart: unless-stopped
    ports:
      - "3060:3060"
      - "3061:3061"
    environment:
      - NODE_ENV=production
      - PORT=3060
      - HOST=0.0.0.0
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=performance_user
      - DB_PASSWORD=performance_password
      - DB_DATABASE=performance_db
    depends_on:
      - redis
      - postgres
    networks:
      - performance-network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3060/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: performance-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redis_password
    environment:
      - REDIS_PASSWORD=redis_password
    networks:
      - performance-network
    volumes:
      - redis-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: performance-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=performance_db
      - POSTGRES_USER=performance_user
      - POSTGRES_PASSWORD=performance_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    networks:
      - performance-network
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U performance_user -d performance_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: performance-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - performance-service
    networks:
      - performance-network
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  performance-network:
    driver: bridge

volumes:
  redis-data:
    driver: local
  postgres-data:
    driver: local
