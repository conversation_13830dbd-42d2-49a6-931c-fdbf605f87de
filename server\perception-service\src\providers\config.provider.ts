/**
 * 配置提供者
 */

import { Provider } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * 感知服务配置接口
 */
export interface PerceptionServiceConfig {
  // 服务配置
  port: number;
  host: string;
  nodeEnv: string;
  
  // Redis配置
  redis: {
    host: string;
    port: number;
    password?: string;
    db: number;
  };
  
  // 处理配置
  processing: {
    enableRealTimeProcessing: boolean;
    batchSize: number;
    fusionThreshold: number;
    anomalyThreshold: number;
    retentionPeriod: number;
    qualityThreshold: number;
    enablePrediction: boolean;
    predictionHorizon: number;
  };
  
  // 监控配置
  monitoring: {
    enableMetrics: boolean;
    metricsInterval: number;
    healthCheckInterval: number;
  };
  
  // 日志配置
  logging: {
    level: string;
    enableFileLogging: boolean;
    logDir: string;
  };
  
  // 安全配置
  security: {
    enableAuth: boolean;
    jwtSecret?: string;
    corsOrigin: string;
  };
  
  // Swagger配置
  swagger: {
    enabled: boolean;
    path: string;
    title: string;
    description: string;
    version: string;
  };
}

/**
 * 配置提供者
 */
export const ConfigProvider: Provider<PerceptionServiceConfig> = {
  provide: 'PERCEPTION_CONFIG',
  useFactory: (configService: ConfigService): PerceptionServiceConfig => {
    return {
      // 服务配置
      port: configService.get<number>('PORT', 3050),
      host: configService.get<string>('HOST', '0.0.0.0'),
      nodeEnv: configService.get<string>('NODE_ENV', 'development'),
      
      // Redis配置
      redis: {
        host: configService.get<string>('REDIS_HOST', 'localhost'),
        port: configService.get<number>('REDIS_PORT', 6379),
        password: configService.get<string>('REDIS_PASSWORD'),
        db: configService.get<number>('REDIS_DB', 0),
      },
      
      // 处理配置
      processing: {
        enableRealTimeProcessing: configService.get<boolean>('ENABLE_REAL_TIME_PROCESSING', true),
        batchSize: configService.get<number>('BATCH_SIZE', 100),
        fusionThreshold: configService.get<number>('FUSION_THRESHOLD', 0.7),
        anomalyThreshold: configService.get<number>('ANOMALY_THRESHOLD', 0.3),
        retentionPeriod: configService.get<number>('RETENTION_PERIOD', 7 * 24 * 60 * 60 * 1000), // 7天
        qualityThreshold: configService.get<number>('QUALITY_THRESHOLD', 0.6),
        enablePrediction: configService.get<boolean>('ENABLE_PREDICTION', true),
        predictionHorizon: configService.get<number>('PREDICTION_HORIZON', 5000), // 5秒
      },
      
      // 监控配置
      monitoring: {
        enableMetrics: configService.get<boolean>('ENABLE_METRICS', true),
        metricsInterval: configService.get<number>('METRICS_INTERVAL', 30000), // 30秒
        healthCheckInterval: configService.get<number>('HEALTH_CHECK_INTERVAL', 10000), // 10秒
      },
      
      // 日志配置
      logging: {
        level: configService.get<string>('LOG_LEVEL', 'info'),
        enableFileLogging: configService.get<boolean>('ENABLE_FILE_LOGGING', false),
        logDir: configService.get<string>('LOG_DIR', './logs'),
      },
      
      // 安全配置
      security: {
        enableAuth: configService.get<boolean>('ENABLE_AUTH', false),
        jwtSecret: configService.get<string>('JWT_SECRET'),
        corsOrigin: configService.get<string>('CORS_ORIGIN', '*'),
      },
      
      // Swagger配置
      swagger: {
        enabled: configService.get<boolean>('SWAGGER_ENABLED', true),
        path: configService.get<string>('SWAGGER_PATH', 'api/docs'),
        title: configService.get<string>('SWAGGER_TITLE', '感知数据处理服务 API'),
        description: configService.get<string>('SWAGGER_DESCRIPTION', '提供大规模感知数据的实时处理、分析和存储功能'),
        version: configService.get<string>('SWAGGER_VERSION', '1.0'),
      },
    };
  },
  inject: [ConfigService],
};
