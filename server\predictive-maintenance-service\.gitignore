# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
*.lcov

# nyc测试覆盖率
.nyc_output

# Grunt中间存储
.grunt

# Bower依赖目录
bower_components

# node-waf配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript v1声明文件
typings/

# TypeScript缓存
*.tsbuildinfo

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# Microbundle缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的REPL历史
.node_repl_history

# yarn包管理器
.yarn-integrity

# dotenv环境变量文件
.env
.env.test
.env.local
.env.production

# parcel-bundler缓存
.cache
.parcel-cache

# Next.js构建输出
.next

# Nuxt.js构建/生成输出
.nuxt
dist

# Gatsby文件
.cache/
public

# Storybook构建输出
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# 日志文件
logs/
*.log

# 上传文件
uploads/

# AI模型文件
models/
*.h5
*.pb
*.onnx

# 数据库文件
*.db
*.sqlite

# 备份文件
*.bak
*.backup

# 临时文件
*.tmp
*.temp

# 系统文件
.DS_Store
Thumbs.db

# IDE文件
.vscode/
.idea/
*.swp
*.swo

# 构建输出
dist/
build/

# 测试输出
test-results/
coverage/

# Docker
.dockerignore

# 监控数据
prometheus_data/
grafana_data/
