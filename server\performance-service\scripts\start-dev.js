/**
 * 开发环境启动脚本
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 启动性能优化服务开发环境...\n');

// 设置环境变量
process.env.NODE_ENV = 'development';
process.env.PORT = '3060';
process.env.HOST = '0.0.0.0';

// 启动开发服务器
const child = spawn('npm', ['run', 'start:dev'], {
  cwd: path.resolve(__dirname, '..'),
  stdio: 'inherit',
  shell: true
});

child.on('error', (error) => {
  console.error('❌ 启动失败:', error);
  process.exit(1);
});

child.on('close', (code) => {
  console.log(`\n📋 服务已停止，退出码: ${code}`);
  process.exit(code);
});

// 优雅关闭处理
process.on('SIGINT', () => {
  console.log('\n🛑 收到停止信号，正在关闭服务...');
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 收到终止信号，正在关闭服务...');
  child.kill('SIGTERM');
});
