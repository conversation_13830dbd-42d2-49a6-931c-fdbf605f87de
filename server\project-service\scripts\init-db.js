/**
 * 数据库初始化脚本
 */
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function initDatabase() {
  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || 'password',
    multipleStatements: true,
  };

  console.log('🔧 开始初始化数据库...');
  console.log(`连接到数据库: ${config.host}:${config.port}`);

  try {
    // 创建数据库连接
    const connection = await mysql.createConnection(config);
    console.log('✅ 数据库连接成功');

    // 读取初始化SQL文件
    const sqlFile = path.join(__dirname, '../migrations/001_initial_schema.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');

    // 执行SQL
    console.log('📝 执行数据库初始化脚本...');
    await connection.execute(sql);
    console.log('✅ 数据库初始化完成');

    // 验证表是否创建成功
    const [tables] = await connection.execute('SHOW TABLES FROM ir_engine_projects');
    console.log('📋 创建的表:');
    tables.forEach(table => {
      console.log(`  - ${Object.values(table)[0]}`);
    });

    await connection.end();
    console.log('🎉 数据库初始化成功完成！');
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase();
}

module.exports = { initDatabase };
