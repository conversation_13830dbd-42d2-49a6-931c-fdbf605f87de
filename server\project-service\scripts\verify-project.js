/**
 * 项目完整性验证脚本
 */
const fs = require('fs');
const path = require('path');

// 必需的文件和目录
const requiredStructure = {
  files: [
    'package.json',
    'tsconfig.json',
    'nest-cli.json',
    '.env.example',
    '.gitignore',
    '.dockerignore',
    '.eslintrc.js',
    '.prettierrc',
    'README.md',
    'Dockerfile',
    'src/main.ts',
    'src/app.module.ts',
    'src/app.controller.ts',
    'src/app.service.ts',
    'src/auth/auth.module.ts',
    'src/auth/strategies/jwt.strategy.ts',
    'src/auth/guards/jwt-auth.guard.ts',
    'src/auth/decorators/public.decorator.ts',
    'src/projects/projects.module.ts',
    'src/projects/projects.controller.ts',
    'src/projects/projects.service.ts',
    'src/projects/entities/project.entity.ts',
    'src/projects/entities/project-member.entity.ts',
    'src/projects/entities/project-setting.entity.ts',
    'src/projects/dto/create-project.dto.ts',
    'src/projects/dto/update-project.dto.ts',
    'src/projects/dto/add-project-member.dto.ts',
    'src/projects/dto/update-project-member.dto.ts',
    'src/projects/dto/create-project-setting.dto.ts',
    'src/scenes/scenes.module.ts',
    'src/scenes/scenes.controller.ts',
    'src/scenes/scenes.service.ts',
    'src/scenes/entities/scene.entity.ts',
    'src/scenes/entities/scene-entity.entity.ts',
    'src/scenes/dto/create-scene.dto.ts',
    'src/scenes/dto/update-scene.dto.ts',
    'src/scenes/dto/create-scene-entity.dto.ts',
    'src/scenes/dto/update-scene-entity.dto.ts',
    'src/health/health.module.ts',
    'src/health/health.controller.ts',
    'src/common/filters/http-exception.filter.ts',
    'src/common/interceptors/logging.interceptor.ts',
    'src/common/interceptors/transform.interceptor.ts',
    'migrations/001_initial_schema.sql',
    'scripts/init-db.js',
    'scripts/seed-data.js',
    'test/app.e2e-spec.ts',
    'test/jest-e2e.json',
  ],
  directories: [
    'src',
    'src/auth',
    'src/auth/strategies',
    'src/auth/guards',
    'src/auth/decorators',
    'src/projects',
    'src/projects/dto',
    'src/projects/entities',
    'src/scenes',
    'src/scenes/dto',
    'src/scenes/entities',
    'src/health',
    'src/common',
    'src/common/filters',
    'src/common/interceptors',
    'migrations',
    'scripts',
    'test',
  ]
};

let allValid = true;
let validCount = 0;
let totalCount = requiredStructure.files.length + requiredStructure.directories.length;

console.log('🔍 验证项目结构完整性...');
console.log('='.repeat(50));

// 检查文件
console.log('\n📁 检查必需文件:');
console.log('-'.repeat(30));

requiredStructure.files.forEach(filePath => {
  const fullPath = path.join(__dirname, '..', filePath);
  const exists = fs.existsSync(fullPath);
  
  if (exists) {
    console.log(`✅ ${filePath}`);
    validCount++;
  } else {
    console.log(`❌ ${filePath} - 文件缺失`);
    allValid = false;
  }
});

// 检查目录
console.log('\n📂 检查必需目录:');
console.log('-'.repeat(30));

requiredStructure.directories.forEach(dirPath => {
  const fullPath = path.join(__dirname, '..', dirPath);
  const exists = fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory();
  
  if (exists) {
    console.log(`✅ ${dirPath}/`);
    validCount++;
  } else {
    console.log(`❌ ${dirPath}/ - 目录缺失`);
    allValid = false;
  }
});

// 检查package.json依赖
console.log('\n📦 检查依赖配置:');
console.log('-'.repeat(30));

try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
  
  const requiredDependencies = [
    '@nestjs/common',
    '@nestjs/core',
    '@nestjs/platform-express',
    '@nestjs/typeorm',
    '@nestjs/config',
    '@nestjs/jwt',
    '@nestjs/passport',
    '@nestjs/microservices',
    '@nestjs/swagger',
    '@nestjs/terminus',
    'typeorm',
    'mysql2',
    'passport-jwt',
    'class-validator',
    'class-transformer',
    'helmet',
    'compression',
  ];
  
  let depsValid = true;
  requiredDependencies.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
    } else {
      console.log(`❌ ${dep} - 依赖缺失`);
      depsValid = false;
      allValid = false;
    }
  });
  
  if (depsValid) {
    console.log('✅ 所有必需依赖都已配置');
  }
} catch (error) {
  console.log(`❌ 无法读取 package.json: ${error.message}`);
  allValid = false;
}

// 检查环境配置
console.log('\n🌍 检查环境配置:');
console.log('-'.repeat(30));

const envExamplePath = path.join(__dirname, '..', '.env.example');
if (fs.existsSync(envExamplePath)) {
  console.log('✅ .env.example 文件存在');
  
  try {
    const envContent = fs.readFileSync(envExamplePath, 'utf8');
    const requiredEnvVars = [
      'NODE_ENV',
      'PROJECT_SERVICE_HOST',
      'PROJECT_SERVICE_PORT',
      'PROJECT_SERVICE_HTTP_PORT',
      'DB_HOST',
      'DB_PORT',
      'DB_USERNAME',
      'DB_PASSWORD',
      'DB_DATABASE',
      'JWT_SECRET',
    ];
    
    requiredEnvVars.forEach(envVar => {
      if (envContent.includes(`${envVar}=`)) {
        console.log(`✅ ${envVar} - 已配置`);
      } else {
        console.log(`⚠️ ${envVar} - 未在示例中配置`);
      }
    });
  } catch (error) {
    console.log(`❌ 无法读取 .env.example: ${error.message}`);
  }
} else {
  console.log('❌ .env.example 文件缺失');
  allValid = false;
}

// 总结
console.log('\n📊 验证结果:');
console.log('='.repeat(50));
console.log(`总计: ${totalCount} 项`);
console.log(`通过: ${validCount} 项`);
console.log(`失败: ${totalCount - validCount} 项`);
console.log(`完整性: ${((validCount / totalCount) * 100).toFixed(1)}%`);

if (allValid) {
  console.log('\n🎉 项目结构完整性验证通过！');
  process.exit(0);
} else {
  console.log('\n⚠️ 项目结构存在问题，请检查上述缺失的文件和目录。');
  process.exit(1);
}
