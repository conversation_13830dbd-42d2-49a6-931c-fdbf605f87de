/**
 * 日志拦截器
 */

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request, Response } from 'express';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    
    const { method, url, ip, headers } = request;
    const userAgent = headers['user-agent'] || '';
    const startTime = Date.now();

    // 记录请求开始
    this.logger.log(
      `请求开始: ${method} ${url} - IP: ${ip} - UserAgent: ${userAgent}`
    );

    return next.handle().pipe(
      tap({
        next: (data) => {
          const endTime = Date.now();
          const duration = endTime - startTime;
          const { statusCode } = response;

          // 记录请求完成
          this.logger.log(
            `请求完成: ${method} ${url} - ${statusCode} - ${duration}ms`
          );

          // 记录响应数据大小（如果有）
          if (data && typeof data === 'object') {
            const dataSize = JSON.stringify(data).length;
            this.logger.debug(`响应数据大小: ${dataSize} bytes`);
          }
        },
        error: (error) => {
          const endTime = Date.now();
          const duration = endTime - startTime;

          // 记录请求错误
          this.logger.error(
            `请求错误: ${method} ${url} - ${duration}ms - ${error.message}`
          );
        },
      })
    );
  }
}
