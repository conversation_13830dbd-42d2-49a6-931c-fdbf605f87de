/**
 * 健康检查控制器
 */

import {
  Controller,
  Get,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';

import { HealthService } from '../services/health.service';
import { HealthResponseDto } from '../dto/health.dto';

/**
 * 健康检查控制器
 */
@ApiTags('health')
@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(private readonly healthService: HealthService) {}

  /**
   * 基础健康检查
   */
  @Get()
  @ApiOperation({ summary: '基础健康检查' })
  @ApiResponse({
    status: 200,
    description: '服务健康',
    type: HealthResponseDto,
  })
  @ApiResponse({ status: 503, description: '服务不健康' })
  async checkHealth(): Promise<HealthResponseDto> {
    try {
      const healthStatus = await this.healthService.checkHealth();

      if (!healthStatus.healthy) {
        throw new HttpException(
          {
            success: false,
            message: '服务不健康',
            data: healthStatus,
          },
          HttpStatus.SERVICE_UNAVAILABLE,
        );
      }

      return {
        success: true,
        message: '服务健康',
        data: healthStatus,
      };
    } catch (error) {
      this.logger.error(`健康检查失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '健康检查失败',
          error: error.message,
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  /**
   * 详细健康检查
   */
  @Get('detailed')
  @ApiOperation({ summary: '详细健康检查' })
  @ApiResponse({
    status: 200,
    description: '详细健康状态',
    type: HealthResponseDto,
  })
  @ApiResponse({ status: 503, description: '服务不健康' })
  async checkDetailedHealth(): Promise<HealthResponseDto> {
    try {
      const healthStatus = await this.healthService.checkDetailedHealth();

      if (!healthStatus.healthy) {
        throw new HttpException(
          {
            success: false,
            message: '服务不健康',
            data: healthStatus,
          },
          HttpStatus.SERVICE_UNAVAILABLE,
        );
      }

      return {
        success: true,
        message: '服务健康',
        data: healthStatus,
      };
    } catch (error) {
      this.logger.error(`详细健康检查失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '详细健康检查失败',
          error: error.message,
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  /**
   * 就绪检查
   */
  @Get('ready')
  @ApiOperation({ summary: '就绪检查' })
  @ApiResponse({ status: 200, description: '服务就绪' })
  @ApiResponse({ status: 503, description: '服务未就绪' })
  async checkReadiness(): Promise<any> {
    try {
      const isReady = await this.healthService.checkReadiness();

      if (!isReady) {
        throw new HttpException(
          {
            success: false,
            message: '服务未就绪',
          },
          HttpStatus.SERVICE_UNAVAILABLE,
        );
      }

      return {
        success: true,
        message: '服务就绪',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`就绪检查失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '就绪检查失败',
          error: error.message,
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  /**
   * 存活检查
   */
  @Get('live')
  @ApiOperation({ summary: '存活检查' })
  @ApiResponse({ status: 200, description: '服务存活' })
  @ApiResponse({ status: 503, description: '服务不存活' })
  async checkLiveness(): Promise<any> {
    try {
      const isAlive = await this.healthService.checkLiveness();

      if (!isAlive) {
        throw new HttpException(
          {
            success: false,
            message: '服务不存活',
          },
          HttpStatus.SERVICE_UNAVAILABLE,
        );
      }

      return {
        success: true,
        message: '服务存活',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`存活检查失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '存活检查失败',
          error: error.message,
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }
}
