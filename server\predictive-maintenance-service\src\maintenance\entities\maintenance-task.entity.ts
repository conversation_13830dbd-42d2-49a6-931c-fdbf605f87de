/**
 * 维护任务实体
 */

import { 
  Entity, 
  PrimaryGeneratedColumn, 
  Column, 
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index
} from 'typeorm';
import { DeviceEntity } from './device.entity';

@Entity('maintenance_tasks')
@Index(['deviceId', 'status'])
@Index(['taskType'])
@Index(['priority'])
@Index(['scheduledDate'])
export class MaintenanceTaskEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'task_id', length: 50, unique: true })
  taskId: string;

  @Column({ name: 'device_id', length: 50 })
  deviceId: string;

  @Column({ length: 200 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ 
    name: 'task_type',
    type: 'enum', 
    enum: ['inspection', 'cleaning', 'lubrication', 'replacement', 'calibration', 'repair', 'upgrade']
  })
  taskType: 'inspection' | 'cleaning' | 'lubrication' | 'replacement' | 'calibration' | 'repair' | 'upgrade';

  @Column({ 
    type: 'enum', 
    enum: ['low', 'medium', 'high', 'urgent']
  })
  priority: 'low' | 'medium' | 'high' | 'urgent';

  @Column({ 
    type: 'enum', 
    enum: ['pending', 'scheduled', 'in_progress', 'completed', 'cancelled', 'overdue'],
    default: 'pending'
  })
  status: 'pending' | 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'overdue';

  @Column({ name: 'scheduled_date', type: 'datetime' })
  scheduledDate: Date;

  @Column({ name: 'due_date', type: 'datetime' })
  dueDate: Date;

  @Column({ name: 'estimated_duration', type: 'int' })
  estimatedDuration: number; // 分钟

  @Column({ name: 'estimated_cost', type: 'decimal', precision: 10, scale: 2, nullable: true })
  estimatedCost: number;

  @Column({ name: 'assigned_to', length: 100, nullable: true })
  assignedTo: string;

  @Column({ name: 'assigned_team', length: 100, nullable: true })
  assignedTeam: string;

  @Column({ name: 'required_skills', type: 'json', nullable: true })
  requiredSkills: string[];

  @Column({ name: 'required_tools', type: 'json', nullable: true })
  requiredTools: string[];

  @Column({ name: 'required_parts', type: 'json', nullable: true })
  requiredParts: string[];

  @Column({ name: 'safety_requirements', type: 'json', nullable: true })
  safetyRequirements: string[];

  @Column({ name: 'started_at', type: 'datetime', nullable: true })
  startedAt: Date;

  @Column({ name: 'completed_at', type: 'datetime', nullable: true })
  completedAt: Date;

  @Column({ name: 'actual_duration', type: 'int', nullable: true })
  actualDuration: number; // 分钟

  @Column({ name: 'actual_cost', type: 'decimal', precision: 10, scale: 2, nullable: true })
  actualCost: number;

  @Column({ name: 'completion_notes', type: 'text', nullable: true })
  completionNotes: string;

  @Column({ name: 'quality_check_passed', type: 'boolean', nullable: true })
  qualityCheckPassed: boolean;

  @Column({ name: 'quality_check_notes', type: 'text', nullable: true })
  qualityCheckNotes: string;

  @Column({ name: 'next_maintenance_date', type: 'datetime', nullable: true })
  nextMaintenanceDate: Date;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => DeviceEntity, device => device.maintenanceTasks)
  @JoinColumn({ name: 'device_id', referencedColumnName: 'deviceId' })
  device: DeviceEntity;
}
