/**
 * 性能监控拦截器
 * 监控请求处理时间和系统资源使用情况
 */

import { Injectable, NestInterceptor, ExecutionContext, CallHandler, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';

interface PerformanceMetrics {
  requestId: string;
  method: string;
  url: string;
  statusCode: number;
  responseTime: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: NodeJS.CpuUsage;
  timestamp: number;
  userAgent?: string;
  ip?: string;
}

@Injectable()
export class PerformanceInterceptor implements NestInterceptor {
  private readonly logger = new Logger(PerformanceInterceptor.name);
  private readonly performanceThresholds = {
    warning: 1000, // 1秒
    error: 5000, // 5秒
  };

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();

    const startTime = Date.now();
    const startCpuUsage = process.cpuUsage();
    const requestId = this.generateRequestId();

    // 添加请求ID到响应头
    response.setHeader('X-Request-ID', requestId);

    return next.handle().pipe(
      tap(() => {
        this.logPerformanceMetrics(request, response, startTime, startCpuUsage, requestId, false);
      }),
      catchError((error) => {
        this.logPerformanceMetrics(request, response, startTime, startCpuUsage, requestId, true);
        throw error;
      }),
    );
  }

  private logPerformanceMetrics(
    request: Request,
    response: Response,
    startTime: number,
    startCpuUsage: NodeJS.CpuUsage,
    requestId: string,
    isError: boolean,
  ): void {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    const endCpuUsage = process.cpuUsage(startCpuUsage);
    const memoryUsage = process.memoryUsage();

    const metrics: PerformanceMetrics = {
      requestId,
      method: request.method,
      url: request.url,
      statusCode: response.statusCode,
      responseTime,
      memoryUsage,
      cpuUsage: endCpuUsage,
      timestamp: endTime,
      userAgent: request.get('User-Agent'),
      ip: request.ip || request.connection.remoteAddress,
    };

    // 根据响应时间确定日志级别
    if (responseTime > this.performanceThresholds.error) {
      this.logger.error('请求处理时间过长', metrics);
    } else if (responseTime > this.performanceThresholds.warning) {
      this.logger.warn('请求处理时间较长', metrics);
    } else {
      this.logger.debug('请求处理完成', {
        requestId,
        method: request.method,
        url: request.url,
        statusCode: response.statusCode,
        responseTime,
      });
    }

    // 发送性能指标到监控系统（可选）
    this.sendMetricsToMonitoring(metrics, isError);
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private sendMetricsToMonitoring(metrics: PerformanceMetrics, isError: boolean): void {
    // 这里可以实现发送指标到监控系统的逻辑
    // 例如发送到 Prometheus、DataDog、New Relic 等

    // 示例：记录到应用级别的性能统计
    if (global.performanceStats) {
      global.performanceStats.recordRequest(metrics.responseTime, isError);
    }
  }
}
