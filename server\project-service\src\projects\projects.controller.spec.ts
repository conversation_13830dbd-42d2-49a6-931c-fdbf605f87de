/**
 * 项目控制器单元测试
 */
import { Test, TestingModule } from '@nestjs/testing';
import { ProjectsController } from './projects.controller';
import { ProjectsService } from './projects.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ProjectVisibility } from './entities/project.entity';

describe('ProjectsController', () => {
  let controller: ProjectsController;
  let service: ProjectsService;

  const mockProject = {
    id: 'project-1',
    name: '测试项目',
    description: '这是一个测试项目',
    ownerId: 'user-1',
    visibility: ProjectVisibility.PRIVATE,
    isTemplate: false,
    isArchived: false,
    members: [],
    settings: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockUser = {
    id: 'user-1',
    username: 'testuser',
    email: '<EMAIL>',
  };

  const mockRequest = {
    user: mockUser,
  };

  beforeEach(async () => {
    const mockProjectsService = {
      create: jest.fn(),
      findAll: jest.fn(),
      findUserProjects: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
      addMember: jest.fn(),
      updateMember: jest.fn(),
      removeMember: jest.fn(),
      createSetting: jest.fn(),
      getSetting: jest.fn(),
      removeSetting: jest.fn(),
      checkPermission: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProjectsController],
      providers: [
        {
          provide: ProjectsService,
          useValue: mockProjectsService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<ProjectsController>(ProjectsController);
    service = module.get<ProjectsService>(ProjectsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a project', async () => {
      const createProjectDto = {
        name: '新项目',
        description: '项目描述',
        visibility: ProjectVisibility.PRIVATE,
      };

      jest.spyOn(service, 'create').mockResolvedValue(mockProject as any);

      const result = await controller.create(mockRequest as any, createProjectDto);

      expect(result).toEqual(mockProject);
      expect(service.create).toHaveBeenCalledWith('user-1', createProjectDto);
    });
  });

  describe('findAll', () => {
    it('should return all projects', async () => {
      const projects = [mockProject];
      jest.spyOn(service, 'findAll').mockResolvedValue(projects as any);

      const result = await controller.findAll(mockRequest as any);

      expect(result).toEqual(projects);
      expect(service.findAll).toHaveBeenCalledWith('user-1');
    });
  });

  describe('findMyProjects', () => {
    it('should return user projects', async () => {
      const projects = [mockProject];
      jest.spyOn(service, 'findUserProjects').mockResolvedValue(projects as any);

      const result = await controller.findMyProjects(mockRequest as any);

      expect(result).toEqual(projects);
      expect(service.findUserProjects).toHaveBeenCalledWith('user-1');
    });
  });

  describe('findOne', () => {
    it('should return a project', async () => {
      jest.spyOn(service, 'findOne').mockResolvedValue(mockProject as any);

      const result = await controller.findOne('project-1');

      expect(result).toEqual(mockProject);
      expect(service.findOne).toHaveBeenCalledWith('project-1');
    });
  });

  describe('update', () => {
    it('should update a project', async () => {
      const updateProjectDto = { name: '更新的项目名称' };
      const updatedProject = { ...mockProject, ...updateProjectDto };

      jest.spyOn(service, 'update').mockResolvedValue(updatedProject as any);

      const result = await controller.update('project-1', mockRequest as any, updateProjectDto);

      expect(result).toEqual(updatedProject);
      expect(service.update).toHaveBeenCalledWith('project-1', 'user-1', updateProjectDto);
    });
  });

  describe('remove', () => {
    it('should remove a project', async () => {
      jest.spyOn(service, 'remove').mockResolvedValue(undefined);

      await controller.remove('project-1', mockRequest as any);

      expect(service.remove).toHaveBeenCalledWith('project-1', 'user-1');
    });
  });
});
