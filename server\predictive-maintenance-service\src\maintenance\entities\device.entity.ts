/**
 * 设备实体
 */

import { 
  Entity, 
  PrimaryGeneratedColumn, 
  Column, 
  CreateDateColumn, 
  UpdateDateColumn,
  OneToMany,
  Index
} from 'typeorm';
import { DeviceHealthRecord } from './device-health-record.entity';
import { FailurePredictionRecord } from './failure-prediction-record.entity';
import { MaintenanceTaskEntity } from './maintenance-task.entity';

@Entity('devices')
@Index(['deviceId'], { unique: true })
@Index(['status'])
@Index(['location'])
export class DeviceEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'device_id', length: 50, unique: true })
  deviceId: string;

  @Column({ length: 100 })
  name: string;

  @Column({ length: 50 })
  type: string;

  @Column({ length: 100, nullable: true })
  model: string;

  @Column({ length: 100, nullable: true })
  manufacturer: string;

  @Column({ name: 'serial_number', length: 100, nullable: true })
  serialNumber: string;

  @Column({ length: 100, nullable: true })
  location: string;

  @Column({ 
    type: 'enum', 
    enum: ['active', 'inactive', 'maintenance', 'fault'],
    default: 'active'
  })
  status: 'active' | 'inactive' | 'maintenance' | 'fault';

  @Column({ name: 'installation_date', type: 'datetime', nullable: true })
  installationDate: Date;

  @Column({ name: 'last_maintenance_date', type: 'datetime', nullable: true })
  lastMaintenanceDate: Date;

  @Column({ name: 'next_maintenance_date', type: 'datetime', nullable: true })
  nextMaintenanceDate: Date;

  @Column({ name: 'operating_hours', type: 'decimal', precision: 10, scale: 2, default: 0 })
  operatingHours: number;

  @Column({ name: 'cycle_count', type: 'bigint', default: 0 })
  cycleCount: number;

  @Column({ type: 'json', nullable: true })
  specifications: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  configuration: Record<string, any>;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => DeviceHealthRecord, record => record.device)
  healthRecords: DeviceHealthRecord[];

  @OneToMany(() => FailurePredictionRecord, prediction => prediction.device)
  failurePredictions: FailurePredictionRecord[];

  @OneToMany(() => MaintenanceTaskEntity, task => task.device)
  maintenanceTasks: MaintenanceTaskEntity[];
}
