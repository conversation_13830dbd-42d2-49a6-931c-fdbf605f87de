/**
 * 健康检查服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);

  constructor(
    private configService: ConfigService,
    private dataSource: DataSource,
  ) {}

  /**
   * 基础健康检查
   */
  async check() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'nlp-scene-service',
      version: '1.0.0',
    };
  }

  /**
   * 详细健康检查
   */
  async detailedCheck() {
    const checks = {
      database: await this.checkDatabase(),
      storage: await this.checkStorage(),
      memory: this.checkMemory(),
      uptime: process.uptime(),
    };

    const isHealthy = Object.values(checks).every(
      check => {
        if (typeof check === 'object' && check !== null && 'status' in check) {
          return check.status === 'ok';
        }
        return true;
      }
    );

    return {
      status: isHealthy ? 'ok' : 'error',
      timestamp: new Date().toISOString(),
      service: 'nlp-scene-service',
      version: '1.0.0',
      checks,
    };
  }

  /**
   * 就绪检查
   */
  async readinessCheck() {
    try {
      // 检查数据库连接
      await this.dataSource.query('SELECT 1');
      
      return {
        status: 'ready',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('就绪检查失败:', error);
      return {
        status: 'not_ready',
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  /**
   * 存活检查
   */
  async livenessCheck() {
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }

  /**
   * 检查数据库连接
   */
  private async checkDatabase() {
    try {
      await this.dataSource.query('SELECT 1');
      return { status: 'ok', message: '数据库连接正常' };
    } catch (error) {
      this.logger.error('数据库检查失败:', error);
      return { status: 'error', message: '数据库连接失败', error: error.message };
    }
  }

  /**
   * 检查存储
   */
  private async checkStorage() {
    try {
      const storagePath = this.configService.get<string>('SCENE_STORAGE_PATH', './storage/scenes');
      // 这里可以添加存储检查逻辑
      return { status: 'ok', message: '存储正常', path: storagePath };
    } catch (error) {
      this.logger.error('存储检查失败:', error);
      return { status: 'error', message: '存储检查失败', error: error.message };
    }
  }

  /**
   * 检查内存使用
   */
  private checkMemory() {
    const memUsage = process.memoryUsage();
    return {
      rss: Math.round(memUsage.rss / 1024 / 1024) + ' MB',
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + ' MB',
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + ' MB',
      external: Math.round(memUsage.external / 1024 / 1024) + ' MB',
    };
  }
}
