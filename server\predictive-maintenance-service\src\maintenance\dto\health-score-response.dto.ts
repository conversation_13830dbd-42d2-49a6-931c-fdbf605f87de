/**
 * 健康评分响应DTO
 */

import { ApiProperty } from '@nestjs/swagger';

export class ComponentScoresDto {
  @ApiProperty({ description: '机械组件评分', example: 85.5 })
  mechanical: number;

  @ApiProperty({ description: '电气组件评分', example: 92.3 })
  electrical: number;

  @ApiProperty({ description: '热力组件评分', example: 78.9 })
  thermal: number;

  @ApiProperty({ description: '振动组件评分', example: 88.1 })
  vibration: number;

  @ApiProperty({ description: '性能组件评分', example: 90.7 })
  performance: number;
}

export class TrendsDto {
  @ApiProperty({ 
    description: '短期趋势', 
    example: 'stable',
    enum: ['improving', 'stable', 'degrading']
  })
  shortTerm: 'improving' | 'stable' | 'degrading';

  @ApiProperty({ 
    description: '长期趋势', 
    example: 'degrading',
    enum: ['improving', 'stable', 'degrading']
  })
  longTerm: 'improving' | 'stable' | 'degrading';
}

export class HealthScoreResponseDto {
  @ApiProperty({ description: '设备ID', example: 'device-001' })
  deviceId: string;

  @ApiProperty({ description: '总体健康评分', example: 87.2 })
  overallScore: number;

  @ApiProperty({ 
    description: '健康状态', 
    example: 'good',
    enum: ['excellent', 'good', 'fair', 'poor', 'critical']
  })
  status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';

  @ApiProperty({ description: '组件评分', type: ComponentScoresDto })
  componentScores: ComponentScoresDto;

  @ApiProperty({ description: '趋势分析', type: TrendsDto })
  trends: TrendsDto;

  @ApiProperty({ description: '最后更新时间', example: '2024-01-01T12:00:00.000Z' })
  lastUpdated: string;
}
