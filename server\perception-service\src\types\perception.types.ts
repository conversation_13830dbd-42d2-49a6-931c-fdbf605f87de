/**
 * 感知数据处理相关类型定义
 */

/**
 * 感知模态枚举
 */
export enum PerceptionModality {
  VISUAL = 'VISUAL',
  AUDITORY = 'AUDITORY',
  SOCIAL = 'SOCIAL',
  ENVIRONMENTAL = 'ENVIRONMENTAL',
  TACTILE = 'TACTILE',
  OLFACTORY = 'OLFACTORY'
}

/**
 * 感知异常接口
 */
export interface PerceptionAnomaly {
  type: string;
  description: string;
  severity: number;
  timestamp: number;
}

/**
 * 感知数据接口
 */
export interface PerceptionData {
  id: string;
  entityId: string;
  sessionId: string;
  modality: PerceptionModality;
  timestamp: number;
  confidence: number;
  data: any;
  metadata: any;
  processed?: boolean;
  anomalies?: PerceptionAnomaly[];
}

/**
 * 融合感知数据接口
 */
export interface FusedPerceptionData {
  id: string;
  entityId: string;
  sessionId: string;
  timestamp: number;
  confidence: number;
  worldModel: any;
  attentionFocus: any[];
  predictions: any[];
  anomalies: PerceptionAnomaly[];
  sourceDataIds: string[];
}

/**
 * 感知数据实体
 */
export interface PerceptionDataEntity {
  id: string;
  entityId: string;
  sessionId: string;
  modality: PerceptionModality;
  timestamp: number;
  confidence: number;
  data: any;
  metadata: any;
  processed: boolean;
  anomalies: PerceptionAnomaly[];
}

/**
 * 融合感知数据实体
 */
export interface FusedPerceptionDataEntity {
  id: string;
  entityId: string;
  sessionId: string;
  timestamp: number;
  confidence: number;
  worldModel: any;
  attentionFocus: any[];
  predictions: any[];
  anomalies: PerceptionAnomaly[];
  sourceDataIds: string[];
}

/**
 * 感知统计数据
 */
export interface PerceptionStatistics {
  totalPerceptions: number;
  modalityBreakdown: { [key: string]: number };
  averageConfidence: number;
  anomalyRate: number;
  processingLatency: number;
  fusionRate: number;
  dataQuality: number;
}

/**
 * 感知处理配置
 */
export interface PerceptionProcessingConfig {
  enableRealTimeProcessing: boolean;
  batchSize: number;
  fusionThreshold: number;
  anomalyThreshold: number;
  retentionPeriod: number;
  qualityThreshold: number;
  enablePrediction: boolean;
  predictionHorizon: number;
}
