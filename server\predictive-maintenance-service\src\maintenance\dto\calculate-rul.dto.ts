/**
 * 计算剩余使用寿命DTO
 */

import { IsString, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CalculateRulDto {
  @ApiProperty({ 
    description: '设备ID', 
    example: 'device-001',
    minLength: 1,
    maxLength: 50
  })
  @IsString()
  deviceId: string;

  @ApiPropertyOptional({ 
    description: '组件名称（可选，不指定则计算整体设备）', 
    example: 'bearing',
    maxLength: 50
  })
  @IsOptional()
  @IsString()
  component?: string;
}
