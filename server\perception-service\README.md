# 感知数据处理服务 (Perception Service)

## 概述

感知数据处理服务是DL引擎生态系统中的核心微服务，专门负责大规模感知数据的实时处理、分析和存储。该服务支持多模态数据融合、异常检测、预测分析等高级功能，为智能系统提供强大的感知能力。

## 主要功能

### 🔍 核心功能
- **实时数据处理**: 支持大规模感知数据的实时处理和分析
- **多模态融合**: 融合视觉、听觉、社交、环境等多种感知模态
- **异常检测**: 智能检测感知数据中的异常和异常模式
- **预测分析**: 基于历史数据进行未来状态预测
- **数据质量评估**: 自动评估和提升感知数据质量

### 🚀 高级特性
- **分布式处理**: 支持分布式环境下的高并发处理
- **实时融合**: 多模态数据的实时融合和世界模型构建
- **注意力机制**: 智能生成注意力焦点，优化处理效率
- **自适应配置**: 动态调整处理参数和策略
- **监控告警**: 全面的性能监控和健康检查

## 技术架构

### 🏗️ 技术栈
- **框架**: NestJS 10.x
- **语言**: TypeScript 5.x
- **缓存**: Redis (数据缓存和消息队列)
- **事件**: EventEmitter2 (事件驱动架构)
- **调度**: @nestjs/schedule (定时任务)
- **验证**: class-validator (数据验证)
- **文档**: Swagger/OpenAPI
- **测试**: Jest (单元测试)

### 📊 支持的感知模态
- **视觉感知** - 对象检测、场景理解、光照分析
- **听觉感知** - 声音识别、环境音分析、语音处理
- **社交感知** - 实体交互、群体行为、社交关系
- **环境感知** - 天气状况、障碍物检测、资源分布

## 快速开始

### 环境要求
- Node.js >= 18.0.0
- Redis >= 6.0.0
- npm >= 8.0.0
- TypeScript >= 5.0.0

### 安装依赖
```bash
npm install
```

### 环境配置
复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的参数：
```env
# 基础配置
NODE_ENV=development
PORT=3050
HOST=0.0.0.0

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# 处理配置
ENABLE_REAL_TIME_PROCESSING=true
BATCH_SIZE=100
FUSION_THRESHOLD=0.7
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod

# 使用Docker
docker-compose up -d
```

## API 接口

### 感知数据处理
```http
POST /api/v1/perception/process
Content-Type: application/json

{
  "id": "perception_001",
  "entityId": "entity_001",
  "sessionId": "session_001",
  "modality": "VISUAL",
  "timestamp": 1640995200000,
  "confidence": 0.85,
  "data": {
    "objects": [...],
    "lighting": {...}
  }
}
```

### 批量处理
```http
POST /api/v1/perception/process/batch
Content-Type: application/json

{
  "data": [
    {
      "id": "perception_001",
      "entityId": "entity_001",
      ...
    }
  ]
}
```

### 获取实体数据
```http
GET /api/v1/perception/entity/{entityId}/data?limit=10
```

### 获取融合数据
```http
GET /api/v1/perception/entity/{entityId}/fused?limit=10
```

### 获取统计信息
```http
GET /api/v1/perception/statistics
```

### 更新配置
```http
PUT /api/v1/perception/config
Content-Type: application/json

{
  "enableRealTimeProcessing": true,
  "batchSize": 150,
  "fusionThreshold": 0.8
}
```

### 健康检查
```http
GET /api/v1/health
GET /api/v1/health/detailed
GET /api/v1/health/ready
GET /api/v1/health/live
```

## 配置说明

### 处理配置
- `ENABLE_REAL_TIME_PROCESSING`: 启用实时处理模式
- `BATCH_SIZE`: 批次处理大小
- `FUSION_THRESHOLD`: 数据融合阈值
- `ANOMALY_THRESHOLD`: 异常检测阈值
- `QUALITY_THRESHOLD`: 数据质量阈值

### 性能配置
- `MAX_CONCURRENT_PROCESSING`: 最大并发处理数
- `MAX_QUEUE_SIZE`: 队列最大长度
- `REQUEST_TIMEOUT`: 请求超时时间

### 监控配置
- `ENABLE_METRICS`: 启用指标收集
- `METRICS_INTERVAL`: 指标收集间隔
- `HEALTH_CHECK_INTERVAL`: 健康检查间隔

## 开发指南

### 项目结构
```
src/
├── controllers/          # 控制器
│   ├── perception.controller.ts
│   └── health.controller.ts
├── services/            # 服务层
│   ├── perception-processing.service.ts
│   └── health.service.ts
├── dto/                 # 数据传输对象
│   ├── perception.dto.ts
│   └── health.dto.ts
├── providers/           # 提供者
│   ├── redis.provider.ts
│   └── config.provider.ts
├── app.module.ts        # 主模块
└── main.ts             # 应用入口
```

### 添加新的感知模态
1. 在 `PerceptionModality` 枚举中添加新模态
2. 在 `PerceptionProcessingService` 中实现模态特定的处理逻辑
3. 添加相应的数据验证和增强方法
4. 更新融合算法以支持新模态
5. 添加测试用例

### 自定义异常检测
1. 在 `detectModalityAnomalies` 方法中添加新的检测逻辑
2. 定义异常类型和严重程度
3. 实现异常处理和恢复策略
4. 添加监控和告警机制

## 部署指南

### Docker部署
```bash
# 构建镜像
docker build -t perception-service .

# 运行容器
docker run -d \
  --name perception-service \
  -p 3050:3050 \
  -e REDIS_HOST=redis \
  perception-service
```

### Kubernetes部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: perception-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: perception-service
  template:
    metadata:
      labels:
        app: perception-service
    spec:
      containers:
      - name: perception-service
        image: perception-service:latest
        ports:
        - containerPort: 3050
        env:
        - name: REDIS_HOST
          value: "redis-service"
```

## 监控和运维

### 健康检查端点
- `/api/v1/health` - 基础健康检查
- `/api/v1/health/detailed` - 详细健康状态
- `/api/v1/health/ready` - 就绪检查
- `/api/v1/health/live` - 存活检查

### 关键指标
- 处理延迟
- 吞吐量
- 错误率
- 队列长度
- 内存使用
- CPU使用

### 日志级别
- `error`: 错误信息
- `warn`: 警告信息
- `info`: 一般信息
- `debug`: 调试信息
- `verbose`: 详细信息

## 故障排除

### 常见问题
1. **Redis连接失败**: 检查Redis服务状态和网络连接
2. **内存使用过高**: 调整批次大小和队列长度
3. **处理延迟过高**: 优化处理算法或增加并发数
4. **数据质量低**: 检查输入数据格式和质量阈值

### 性能优化
1. 调整批次处理大小
2. 优化Redis配置
3. 使用连接池
4. 启用数据压缩
5. 配置适当的缓存策略

## 许可证

MIT License

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

- 项目维护者: DL Engine Team
- 邮箱: <EMAIL>
- 文档: https://docs.dlengine.com
