/**
 * Redis配置
 */

import { registerAs } from '@nestjs/config';

export default registerAs('redis', () => ({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT, 10) || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB, 10) || 0,
  retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY, 10) || 100,
  enableReadyCheck: process.env.REDIS_ENABLE_READY_CHECK !== 'false',
  maxRetriesPerRequest: process.env.REDIS_MAX_RETRIES
    ? parseInt(process.env.REDIS_MAX_RETRIES, 10)
    : null,
  lazyConnect: process.env.REDIS_LAZY_CONNECT === 'true',
  keepAlive: parseInt(process.env.REDIS_KEEP_ALIVE, 10) || 30000,
  connectTimeout: parseInt(process.env.REDIS_CONNECT_TIMEOUT, 10) || 10000,
  commandTimeout: parseInt(process.env.REDIS_COMMAND_TIMEOUT, 10) || 5000,
  family: parseInt(process.env.REDIS_FAMILY, 10) || 4,
}));
