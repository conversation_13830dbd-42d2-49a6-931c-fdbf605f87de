{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "moduleNameMapping": {"^@/(.*)$": "<rootDir>/../src/$1", "^@controllers/(.*)$": "<rootDir>/../src/controllers/$1", "^@services/(.*)$": "<rootDir>/../src/services/$1", "^@dto/(.*)$": "<rootDir>/../src/dto/$1", "^@guards/(.*)$": "<rootDir>/../src/guards/$1", "^@config/(.*)$": "<rootDir>/../src/config/$1", "^@constants/(.*)$": "<rootDir>/../src/constants/$1"}}