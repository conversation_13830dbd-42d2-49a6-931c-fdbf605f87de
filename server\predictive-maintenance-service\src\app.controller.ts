/**
 * 预测性维护服务主控制器
 */

import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('应用信息')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: '获取服务信息' })
  @ApiResponse({ 
    status: 200, 
    description: '服务信息获取成功',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: '预测性维护服务' },
        version: { type: 'string', example: '1.0.0' },
        description: { type: 'string', example: '基于AI的预测性维护服务' },
        status: { type: 'string', example: 'running' },
        timestamp: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
        uptime: { type: 'number', example: 3600 },
        environment: { type: 'string', example: 'development' }
      }
    }
  })
  getServiceInfo() {
    return this.appService.getServiceInfo();
  }

  @Get('version')
  @ApiOperation({ summary: '获取服务版本' })
  @ApiResponse({ 
    status: 200, 
    description: '版本信息获取成功',
    schema: {
      type: 'object',
      properties: {
        version: { type: 'string', example: '1.0.0' },
        buildTime: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
        gitCommit: { type: 'string', example: 'abc123' },
        nodeVersion: { type: 'string', example: 'v18.17.0' }
      }
    }
  })
  getVersion() {
    return this.appService.getVersion();
  }
}
