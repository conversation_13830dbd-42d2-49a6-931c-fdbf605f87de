/**
 * 维护建议响应DTO
 */

import { ApiProperty } from '@nestjs/swagger';

export class MaintenanceRecommendationResponseDto {
  @ApiProperty({ description: '设备ID', example: 'device-001' })
  deviceId: string;

  @ApiProperty({ 
    description: '维护类型', 
    example: 'predictive',
    enum: ['preventive', 'predictive', 'corrective', 'emergency']
  })
  maintenanceType: 'preventive' | 'predictive' | 'corrective' | 'emergency';

  @ApiProperty({ 
    description: '优先级', 
    example: 'high',
    enum: ['low', 'medium', 'high', 'urgent']
  })
  priority: 'low' | 'medium' | 'high' | 'urgent';

  @ApiProperty({ description: '维护描述', example: '更换轴承并检查润滑系统' })
  description: string;

  @ApiProperty({ description: '预计维护时长（小时）', example: 4 })
  estimatedDuration: number;

  @ApiProperty({ description: '预计维护成本', example: 2500 })
  estimatedCost: number;

  @ApiProperty({ 
    description: '所需技能', 
    example: ['机械维修', '轴承更换'],
    type: [String]
  })
  requiredSkills: string[];

  @ApiProperty({ 
    description: '所需零件', 
    example: ['轴承', '润滑油'],
    type: [String]
  })
  requiredParts: string[];

  @ApiProperty({ description: '计划维护时间', example: '2024-01-05T09:00:00.000Z' })
  scheduledDate: string;

  @ApiProperty({ description: '维护截止时间', example: '2024-01-07T18:00:00.000Z' })
  deadline: string;

  @ApiProperty({ description: '延期风险', example: '设备可能因轴承故障而停机' })
  riskIfDeferred: string;
}
