/**
 * 故障预测响应DTO
 */

import { ApiProperty } from '@nestjs/swagger';

export class FailurePredictionResponseDto {
  @ApiProperty({ description: '设备ID', example: 'device-001' })
  deviceId: string;

  @ApiProperty({ 
    description: '故障类型', 
    example: 'mechanical',
    enum: ['mechanical', 'electrical', 'hydraulic', 'pneumatic', 'software', 'sensor', 'wear', 'overheating']
  })
  failureType: 'mechanical' | 'electrical' | 'hydraulic' | 'pneumatic' | 'software' | 'sensor' | 'wear' | 'overheating';

  @ApiProperty({ description: '故障概率', example: 0.75, minimum: 0, maximum: 1 })
  probability: number;

  @ApiProperty({ description: '预计故障时间（小时）', example: 72 })
  timeToFailure: number;

  @ApiProperty({ description: '预测置信度', example: 0.85, minimum: 0, maximum: 1 })
  confidence: number;

  @ApiProperty({ 
    description: '影响因素', 
    example: ['高温运行', '振动异常'],
    type: [String]
  })
  contributingFactors: string[];

  @ApiProperty({ 
    description: '建议措施', 
    example: ['检查冷却系统', '更换轴承'],
    type: [String]
  })
  recommendedActions: string[];

  @ApiProperty({ 
    description: '严重程度', 
    example: 'high',
    enum: ['low', 'medium', 'high']
  })
  severity: 'low' | 'medium' | 'high';

  @ApiProperty({ description: '预测时间', example: '2024-01-01T12:00:00.000Z' })
  timestamp: string;
}
