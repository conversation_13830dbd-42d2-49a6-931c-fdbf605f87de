/**
 * 预测性维护服务主服务
 */

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppService {
  private readonly startTime = Date.now();

  constructor(private readonly configService: ConfigService) {}

  /**
   * 获取服务信息
   */
  getServiceInfo() {
    return {
      name: '预测性维护服务',
      version: '1.0.0',
      description: '基于AI的工业设备预测性维护服务',
      status: 'running',
      timestamp: new Date().toISOString(),
      uptime: Math.floor((Date.now() - this.startTime) / 1000),
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      features: [
        '设备健康状态分析',
        '故障预测',
        '维护建议生成',
        '剩余使用寿命计算',
        '异常检测',
        'AI模型训练',
        '实时监控'
      ],
      endpoints: {
        health: '/api/v1/health',
        docs: '/api/docs',
        maintenance: '/api/v1/maintenance'
      }
    };
  }

  /**
   * 获取版本信息
   */
  getVersion() {
    return {
      version: '1.0.0',
      buildTime: new Date().toISOString(),
      gitCommit: process.env.GIT_COMMIT || 'unknown',
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      dependencies: {
        nestjs: '^9.4.3',
        tensorflow: '^4.10.0',
        typeorm: '^0.3.17',
        redis: '^4.6.7'
      }
    };
  }
}
