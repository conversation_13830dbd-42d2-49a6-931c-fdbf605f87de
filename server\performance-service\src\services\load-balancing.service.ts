/**
 * 负载均衡服务
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron } from '@nestjs/schedule';

import { RedisService } from './redis.service';
import {
  CACHE_KEYS,
  LOAD_BALANCE_STRATEGIES,
  PERFORMANCE_EVENTS,
} from '../constants/performance.constants';

interface NodeInfo {
  nodeId: string;
  cpuUsage: number;
  memoryUsage: number;
  activeConnections: number;
  requestCount: number;
  lastSeen: number;
  weight: number;
}

interface LoadBalanceResult {
  selectedNode: string;
  reason: string;
  allNodes: NodeInfo[];
}

@Injectable()
export class LoadBalancingService {
  private readonly logger = new Logger(LoadBalancingService.name);
  private roundRobinIndex = 0;

  constructor(
    private readonly redisService: RedisService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * 选择最佳节点
   */
  async selectNode(
    strategy: string = LOAD_BALANCE_STRATEGIES.CPU_BASED,
  ): Promise<LoadBalanceResult> {
    try {
      const nodes = await this.getActiveNodes();

      if (nodes.length === 0) {
        throw new Error('没有可用的节点');
      }

      if (nodes.length === 1) {
        return {
          selectedNode: nodes[0].nodeId,
          reason: '只有一个可用节点',
          allNodes: nodes,
        };
      }

      let selectedNode: NodeInfo;
      let reason: string;

      switch (strategy) {
        case LOAD_BALANCE_STRATEGIES.ROUND_ROBIN:
          selectedNode = this.selectByRoundRobin(nodes);
          reason = '轮询策略';
          break;
        case LOAD_BALANCE_STRATEGIES.LEAST_CONNECTIONS:
          selectedNode = this.selectByLeastConnections(nodes);
          reason = '最少连接策略';
          break;
        case LOAD_BALANCE_STRATEGIES.CPU_BASED:
          selectedNode = this.selectByCpuUsage(nodes);
          reason = 'CPU使用率策略';
          break;
        case LOAD_BALANCE_STRATEGIES.MEMORY_BASED:
          selectedNode = this.selectByMemoryUsage(nodes);
          reason = '内存使用率策略';
          break;
        default:
          selectedNode = this.selectByCpuUsage(nodes);
          reason = '默认CPU使用率策略';
      }

      // 更新节点请求计数
      await this.updateNodeRequestCount(selectedNode.nodeId);

      const result = {
        selectedNode: selectedNode.nodeId,
        reason,
        allNodes: nodes,
      };

      // 发送负载均衡事件
      this.eventEmitter.emit(PERFORMANCE_EVENTS.LOAD_BALANCE_UPDATED, result);

      return result;
    } catch (error) {
      this.logger.error('选择节点失败:', error);
      throw error;
    }
  }

  /**
   * 注册节点
   */
  async registerNode(nodeInfo: Partial<NodeInfo>): Promise<void> {
    try {
      const node: NodeInfo = {
        nodeId: nodeInfo.nodeId,
        cpuUsage: nodeInfo.cpuUsage || 0,
        memoryUsage: nodeInfo.memoryUsage || 0,
        activeConnections: nodeInfo.activeConnections || 0,
        requestCount: nodeInfo.requestCount || 0,
        lastSeen: Date.now(),
        weight: 1,
      };

      const cacheKey = `${CACHE_KEYS.LOAD_BALANCE}:nodes:${node.nodeId}`;
      await this.redisService.set(cacheKey, node, 300); // 5分钟过期

      this.logger.debug(`节点已注册: ${node.nodeId}`);
    } catch (error) {
      this.logger.error('注册节点失败:', error);
      throw error;
    }
  }

  /**
   * 更新节点状态
   */
  async updateNodeStatus(nodeId: string, status: Partial<NodeInfo>): Promise<void> {
    try {
      const cacheKey = `${CACHE_KEYS.LOAD_BALANCE}:nodes:${nodeId}`;
      const existingNode = await this.redisService.get<NodeInfo>(cacheKey);

      if (!existingNode) {
        this.logger.warn(`节点不存在: ${nodeId}`);
        return;
      }

      const updatedNode: NodeInfo = {
        ...existingNode,
        ...status,
        lastSeen: Date.now(),
      };

      await this.redisService.set(cacheKey, updatedNode, 300);

      this.logger.debug(`节点状态已更新: ${nodeId}`);
    } catch (error) {
      this.logger.error('更新节点状态失败:', error);
      throw error;
    }
  }

  /**
   * 移除节点
   */
  async removeNode(nodeId: string): Promise<void> {
    try {
      const cacheKey = `${CACHE_KEYS.LOAD_BALANCE}:nodes:${nodeId}`;
      await this.redisService.del(cacheKey);

      this.logger.log(`节点已移除: ${nodeId}`);
    } catch (error) {
      this.logger.error('移除节点失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有活跃节点
   */
  async getActiveNodes(): Promise<NodeInfo[]> {
    try {
      const pattern = `${CACHE_KEYS.LOAD_BALANCE}:nodes:*`;
      const keys = await this.redisService.keys(pattern);

      const nodes: NodeInfo[] = [];
      const now = Date.now();
      const maxAge = 60000; // 1分钟

      for (const key of keys) {
        const node = await this.redisService.get<NodeInfo>(key);
        if (node && now - node.lastSeen < maxAge) {
          nodes.push(node);
        }
      }

      return nodes;
    } catch (error) {
      this.logger.error('获取活跃节点失败:', error);
      return [];
    }
  }

  /**
   * 轮询选择
   */
  private selectByRoundRobin(nodes: NodeInfo[]): NodeInfo {
    const selectedNode = nodes[this.roundRobinIndex % nodes.length];
    this.roundRobinIndex++;
    return selectedNode;
  }

  /**
   * 最少连接选择
   */
  private selectByLeastConnections(nodes: NodeInfo[]): NodeInfo {
    return nodes.reduce((min, node) =>
      node.activeConnections < min.activeConnections ? node : min,
    );
  }

  /**
   * 基于CPU使用率选择
   */
  private selectByCpuUsage(nodes: NodeInfo[]): NodeInfo {
    return nodes.reduce((min, node) => (node.cpuUsage < min.cpuUsage ? node : min));
  }

  /**
   * 基于内存使用率选择
   */
  private selectByMemoryUsage(nodes: NodeInfo[]): NodeInfo {
    return nodes.reduce((min, node) => (node.memoryUsage < min.memoryUsage ? node : min));
  }

  /**
   * 更新节点请求计数
   */
  private async updateNodeRequestCount(nodeId: string): Promise<void> {
    try {
      const cacheKey = `${CACHE_KEYS.LOAD_BALANCE}:nodes:${nodeId}`;
      const node = await this.redisService.get<NodeInfo>(cacheKey);

      if (node) {
        node.requestCount++;
        await this.redisService.set(cacheKey, node, 300);
      }
    } catch (error) {
      this.logger.error('更新请求计数失败:', error);
    }
  }

  /**
   * 定期清理过期节点
   */
  @Cron('*/5 * * * *') // 每5分钟执行一次
  async cleanupExpiredNodes(): Promise<void> {
    try {
      const pattern = `${CACHE_KEYS.LOAD_BALANCE}:nodes:*`;
      const keys = await this.redisService.keys(pattern);

      const now = Date.now();
      const maxAge = 300000; // 5分钟
      let cleanedCount = 0;

      for (const key of keys) {
        const node = await this.redisService.get<NodeInfo>(key);
        if (!node || now - node.lastSeen > maxAge) {
          await this.redisService.del(key);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        this.logger.log(`清理过期节点: ${cleanedCount} 个`);
      }
    } catch (error) {
      this.logger.error('清理过期节点失败:', error);
    }
  }

  /**
   * 获取负载均衡统计
   */
  async getLoadBalanceStats(): Promise<any> {
    try {
      const nodes = await this.getActiveNodes();

      if (nodes.length === 0) {
        return {
          totalNodes: 0,
          totalRequests: 0,
          averageCpuUsage: 0,
          averageMemoryUsage: 0,
          averageConnections: 0,
        };
      }

      const totalRequests = nodes.reduce((sum, node) => sum + node.requestCount, 0);
      const averageCpuUsage = nodes.reduce((sum, node) => sum + node.cpuUsage, 0) / nodes.length;
      const averageMemoryUsage =
        nodes.reduce((sum, node) => sum + node.memoryUsage, 0) / nodes.length;
      const averageConnections =
        nodes.reduce((sum, node) => sum + node.activeConnections, 0) / nodes.length;

      return {
        totalNodes: nodes.length,
        totalRequests,
        averageCpuUsage: Math.round(averageCpuUsage * 100) / 100,
        averageMemoryUsage: Math.round(averageMemoryUsage * 100) / 100,
        averageConnections: Math.round(averageConnections),
        nodes: nodes.map((node) => ({
          nodeId: node.nodeId,
          cpuUsage: node.cpuUsage,
          memoryUsage: node.memoryUsage,
          activeConnections: node.activeConnections,
          requestCount: node.requestCount,
          lastSeen: node.lastSeen,
        })),
      };
    } catch (error) {
      this.logger.error('获取负载均衡统计失败:', error);
      throw error;
    }
  }
}
