/**
 * 维护建议记录实体
 */

import { 
  Entity, 
  PrimaryGeneratedColumn, 
  Column, 
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index
} from 'typeorm';
import { DeviceEntity } from './device.entity';

@Entity('maintenance_recommendation_records')
@Index(['deviceId', 'createdAt'])
@Index(['maintenanceType'])
@Index(['priority'])
@Index(['status'])
export class MaintenanceRecommendationRecord {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'device_id', length: 50 })
  deviceId: string;

  @Column({ 
    name: 'maintenance_type',
    type: 'enum', 
    enum: ['preventive', 'predictive', 'corrective', 'emergency']
  })
  maintenanceType: 'preventive' | 'predictive' | 'corrective' | 'emergency';

  @Column({ 
    type: 'enum', 
    enum: ['low', 'medium', 'high', 'urgent']
  })
  priority: 'low' | 'medium' | 'high' | 'urgent';

  @Column({ type: 'text' })
  description: string;

  @Column({ name: 'estimated_duration', type: 'decimal', precision: 8, scale: 2 })
  estimatedDuration: number;

  @Column({ name: 'estimated_cost', type: 'decimal', precision: 10, scale: 2 })
  estimatedCost: number;

  @Column({ name: 'required_skills', type: 'json' })
  requiredSkills: string[];

  @Column({ name: 'required_parts', type: 'json' })
  requiredParts: string[];

  @Column({ name: 'scheduled_date', type: 'datetime' })
  scheduledDate: Date;

  @Column({ type: 'datetime' })
  deadline: Date;

  @Column({ name: 'risk_if_deferred', type: 'text' })
  riskIfDeferred: string;

  @Column({ 
    type: 'enum', 
    enum: ['pending', 'approved', 'scheduled', 'in_progress', 'completed', 'cancelled'],
    default: 'pending'
  })
  status: 'pending' | 'approved' | 'scheduled' | 'in_progress' | 'completed' | 'cancelled';

  @Column({ name: 'assigned_to', length: 100, nullable: true })
  assignedTo: string;

  @Column({ name: 'approved_by', length: 100, nullable: true })
  approvedBy: string;

  @Column({ name: 'approved_at', type: 'datetime', nullable: true })
  approvedAt: Date;

  @Column({ name: 'started_at', type: 'datetime', nullable: true })
  startedAt: Date;

  @Column({ name: 'completed_at', type: 'datetime', nullable: true })
  completedAt: Date;

  @Column({ name: 'actual_duration', type: 'decimal', precision: 8, scale: 2, nullable: true })
  actualDuration: number;

  @Column({ name: 'actual_cost', type: 'decimal', precision: 10, scale: 2, nullable: true })
  actualCost: number;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ name: 'completion_notes', type: 'text', nullable: true })
  completionNotes: string;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => DeviceEntity, device => device.maintenanceTasks)
  @JoinColumn({ name: 'device_id', referencedColumnName: 'deviceId' })
  device: DeviceEntity;
}
