/**
 * 性能相关常量
 */

// 性能指标类型
export const PERFORMANCE_METRIC_TYPES = {
  CPU: 'cpu',
  MEMORY: 'memory',
  NETWORK: 'network',
  APPLICATION: 'application',
  OPTIMIZATION: 'optimization',
} as const;

// 优化建议类型
export const OPTIMIZATION_RECOMMENDATION_TYPES = {
  CRITICAL: 'critical',
  WARNING: 'warning',
  INFO: 'info',
} as const;

// 优化建议分类
export const OPTIMIZATION_CATEGORIES = {
  MEMORY: 'memory',
  CPU: 'cpu',
  NETWORK: 'network',
  APPLICATION: 'application',
} as const;

// 影响级别
export const IMPACT_LEVELS = {
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low',
} as const;

// 负载均衡策略
export const LOAD_BALANCE_STRATEGIES = {
  ROUND_ROBIN: 'round_robin',
  LEAST_CONNECTIONS: 'least_connections',
  CPU_BASED: 'cpu_based',
  MEMORY_BASED: 'memory_based',
} as const;

// 节点状态
export const NODE_STATUS = {
  HEALTHY: 'healthy',
  WARNING: 'warning',
  CRITICAL: 'critical',
  OFFLINE: 'offline',
} as const;

// 性能阈值
export const PERFORMANCE_THRESHOLDS = {
  CPU: {
    WARNING: 70,
    CRITICAL: 85,
  },
  MEMORY: {
    WARNING: 75,
    CRITICAL: 90,
  },
  RESPONSE_TIME: {
    WARNING: 1000,
    CRITICAL: 3000,
  },
  ERROR_RATE: {
    WARNING: 2,
    CRITICAL: 5,
  },
} as const;

// 缓存键前缀
export const CACHE_KEYS = {
  PERFORMANCE_METRICS: 'performance:metrics',
  OPTIMIZATION_CONFIG: 'performance:config',
  NODE_STATUS: 'performance:node',
  LOAD_BALANCE: 'performance:loadbalance',
} as const;

// 事件类型
export const PERFORMANCE_EVENTS = {
  METRICS_COLLECTED: 'performance.metrics.collected',
  THRESHOLD_EXCEEDED: 'performance.threshold.exceeded',
  OPTIMIZATION_APPLIED: 'performance.optimization.applied',
  NODE_STATUS_CHANGED: 'performance.node.status.changed',
  LOAD_BALANCE_UPDATED: 'performance.loadbalance.updated',
} as const;

// 默认配置值
export const DEFAULT_CONFIG = {
  METRICS_INTERVAL: 5000,
  CACHE_TTL: 3600,
  MAX_METRICS_HISTORY: 1000,
  REBALANCE_INTERVAL: 30000,
  HEALTH_CHECK_INTERVAL: 10000,
} as const;

// 时间周期
export const TIME_PERIODS = {
  ONE_HOUR: 60 * 60 * 1000,
  SIX_HOURS: 6 * 60 * 60 * 1000,
  ONE_DAY: 24 * 60 * 60 * 1000,
  ONE_WEEK: 7 * 24 * 60 * 60 * 1000,
} as const;

// HTTP状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;
