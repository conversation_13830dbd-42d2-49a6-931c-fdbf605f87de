version: '3.8'

services:
  # 开发环境性能优化服务
  performance-service-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: performance-service-dev
    restart: unless-stopped
    ports:
      - "3060:3060"
      - "3061:3061"
      - "9229:9229" # Node.js调试端口
    environment:
      - NODE_ENV=development
      - PORT=3060
      - HOST=0.0.0.0
      - REDIS_HOST=redis-dev
      - REDIS_PORT=6379
      - DB_HOST=postgres-dev
      - DB_PORT=5432
      - DB_USERNAME=performance_user
      - DB_PASSWORD=performance_password
      - DB_DATABASE=performance_db
      - DB_SYNCHRONIZE=true
      - DB_LOGGING=true
    depends_on:
      - redis-dev
      - postgres-dev
    networks:
      - performance-dev-network
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
    command: npm run start:debug

  # 开发环境Redis
  redis-dev:
    image: redis:7-alpine
    container_name: performance-redis-dev
    restart: unless-stopped
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes
    networks:
      - performance-dev-network
    volumes:
      - redis-dev-data:/data

  # 开发环境PostgreSQL
  postgres-dev:
    image: postgres:15-alpine
    container_name: performance-postgres-dev
    restart: unless-stopped
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_DB=performance_db
      - POSTGRES_USER=performance_user
      - POSTGRES_PASSWORD=performance_password
    networks:
      - performance-dev-network
    volumes:
      - postgres-dev-data:/var/lib/postgresql/data

  # Redis管理界面
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis-dev:6379
    depends_on:
      - redis-dev
    networks:
      - performance-dev-network

  # PostgreSQL管理界面
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pgadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
    depends_on:
      - postgres-dev
    networks:
      - performance-dev-network
    volumes:
      - pgadmin-data:/var/lib/pgadmin

networks:
  performance-dev-network:
    driver: bridge

volumes:
  redis-dev-data:
    driver: local
  postgres-dev-data:
    driver: local
  pgadmin-data:
    driver: local
