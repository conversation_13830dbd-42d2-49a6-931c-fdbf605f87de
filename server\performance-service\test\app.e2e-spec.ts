/**
 * 应用集成测试
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('AppController (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/health (GET)', () => {
    it('应该返回健康状态', () => {
      return request(app.getHttpServer())
        .get('/health')
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toHaveProperty('status');
          expect(res.body.data).toHaveProperty('timestamp');
          expect(res.body.data).toHaveProperty('uptime');
        });
    });
  });

  describe('/health/detailed (GET)', () => {
    it('应该返回详细健康状态', () => {
      return request(app.getHttpServer())
        .get('/health/detailed')
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toHaveProperty('status');
          expect(res.body.data).toHaveProperty('checks');
          expect(res.body.data).toHaveProperty('system');
        });
    });
  });

  describe('/health/ready (GET)', () => {
    it('应该返回就绪状态', () => {
      return request(app.getHttpServer())
        .get('/health/ready')
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toHaveProperty('ready');
        });
    });
  });

  describe('/health/live (GET)', () => {
    it('应该返回存活状态', () => {
      return request(app.getHttpServer())
        .get('/health/live')
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.status).toBe('alive');
          expect(res.body.data).toHaveProperty('timestamp');
          expect(res.body.data).toHaveProperty('uptime');
        });
    });
  });

  describe('/api/performance/health (GET)', () => {
    it('应该返回性能服务健康状态', () => {
      return request(app.getHttpServer())
        .get('/api/performance/health')
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.status).toBe('healthy');
        });
    });
  });

  // 注意：以下测试需要认证，在实际测试中需要提供有效的JWT令牌
  describe('认证保护的端点', () => {
    it('/api/performance/metrics (GET) 应该要求认证', () => {
      return request(app.getHttpServer()).get('/api/performance/metrics').expect(401);
    });

    it('/api/performance/config/test-node (GET) 应该要求认证', () => {
      return request(app.getHttpServer()).get('/api/performance/config/test-node').expect(401);
    });

    it('/api/performance/recommendations/test-node (GET) 应该要求认证', () => {
      return request(app.getHttpServer())
        .get('/api/performance/recommendations/test-node')
        .expect(401);
    });
  });
});
