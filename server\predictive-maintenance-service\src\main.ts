/**
 * 预测性维护服务启动文件
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
// import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { AppModule } from './app.module';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  try {
    // 创建应用实例
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });

    const configService = app.get(ConfigService);

    // 设置全局前缀
    app.setGlobalPrefix('api/v1');

    // 启用CORS
    app.enableCors({
      origin: configService.get<string>('CORS_ORIGIN', '*'),
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    });

    // 全局验证管道
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );

    // 全局异常过滤器
    app.useGlobalFilters(new GlobalExceptionFilter());

    // 全局拦截器
    app.useGlobalInterceptors(new LoggingInterceptor());

    // Swagger API文档配置
    if (configService.get<string>('NODE_ENV') !== 'production') {
      const config = new DocumentBuilder()
        .setTitle('预测性维护服务 API')
        .setDescription('基于AI的工业设备预测性维护服务API文档')
        .setVersion('1.0.0')
        .addTag('maintenance', '维护管理')
        .addTag('prediction', '故障预测')
        .addTag('health', '健康检查')
        .addBearerAuth()
        .addServer('http://localhost:3020', '开发环境')
        .addServer('https://api.example.com', '生产环境')
        .build();

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup('api/docs', app, document, {
        swaggerOptions: {
          persistAuthorization: true,
          tagsSorter: 'alpha',
          operationsSorter: 'alpha',
        },
      });

      logger.log('📖 Swagger文档已启用: /api/docs');
    }

    // 微服务配置 (暂时禁用，避免依赖问题)
    // const microservicePort = configService.get<number>('MICROSERVICE_PORT', 3021);
    // const microserviceOptions: MicroserviceOptions = {
    //   transport: Transport.TCP,
    //   options: {
    //     host: '0.0.0.0',
    //     port: microservicePort,
    //   },
    // };

    // 连接微服务
    // app.connectMicroservice(microserviceOptions);
    // await app.startAllMicroservices();
    // logger.log(`微服务已启动在端口: ${microservicePort}`);

    // 启动HTTP服务
    const port = configService.get<number>('PORT', 3020);
    const host = configService.get<string>('HOST', '0.0.0.0');

    await app.listen(port, host);

    logger.log(`🚀 预测性维护服务已启动`);
    logger.log(`📍 HTTP服务地址: http://${host}:${port}`);
    logger.log(`📖 API文档地址: http://${host}:${port}/api/docs`);
    logger.log(`🔍 健康检查: http://${host}:${port}/api/v1/health`);
    logger.log(`🔧 环境: ${configService.get<string>('NODE_ENV', 'development')}`);

    // 优雅关闭处理
    process.on('SIGTERM', async () => {
      logger.log('收到SIGTERM信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.log('收到SIGINT信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

  } catch (error) {
    logger.error('服务启动失败:', error);
    process.exit(1);
  }
}

bootstrap();
