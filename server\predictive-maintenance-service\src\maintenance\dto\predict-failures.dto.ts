/**
 * 预测设备故障DTO
 */

import { IsString, IsOptional, <PERSON><PERSON><PERSON>ber, <PERSON>, Max } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class PredictFailuresDto {
  @ApiProperty({ 
    description: '设备ID', 
    example: 'device-001',
    minLength: 1,
    maxLength: 50
  })
  @IsString()
  deviceId: string;

  @ApiPropertyOptional({ 
    description: '预测时间范围（小时）', 
    example: 168,
    minimum: 1,
    maximum: 8760,
    default: 168
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(8760) // 最大1年
  predictionHorizon?: number = 168; // 默认7天
}
