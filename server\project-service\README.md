# DL引擎项目服务 (Project Service)

DL（Digital Learning）引擎的项目管理微服务，负责处理项目、场景和相关资源的管理。

## 功能特性

### 🎯 核心功能
- **项目管理**: 创建、编辑、删除和查看3D项目
- **场景管理**: 管理项目中的3D场景和场景实体
- **权限控制**: 基于角色的项目访问控制
- **协作功能**: 多用户项目协作和成员管理
- **模板系统**: 项目模板创建和使用
- **设置管理**: 项目级别的配置管理

### 🔐 安全特性
- JWT认证和授权
- 基于角色的访问控制 (RBAC)
- 请求验证和数据校验
- 全局异常处理

### 📊 监控和日志
- 请求日志记录
- 性能监控
- 健康检查端点
- Swagger API文档

## 技术栈

- **框架**: NestJS 9.x
- **数据库**: MySQL 8.0+
- **ORM**: TypeORM
- **认证**: JWT + Passport
- **文档**: Swagger/OpenAPI
- **测试**: Jest
- **容器**: Docker

## 快速开始

### 环境要求
- Node.js >= 18.0.0
- MySQL >= 8.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 环境配置
复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的参数：
```env
# 基础配置
NODE_ENV=development
PROJECT_SERVICE_HOST=localhost
PROJECT_SERVICE_PORT=3002
PROJECT_SERVICE_HTTP_PORT=4002

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=ir_engine_projects

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=1d
```

### 数据库初始化
```bash
# 初始化数据库结构
node scripts/init-db.js

# 插入示例数据（可选）
node scripts/seed-data.js
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产构建
npm run build

# 生产运行
npm run start:prod
```

## API接口

### 认证
所有API接口（除健康检查外）都需要JWT认证：
```http
Authorization: Bearer <your-jwt-token>
```

### 项目管理

#### 创建项目
```http
POST /api/projects
Content-Type: application/json

{
  "name": "我的3D项目",
  "description": "项目描述",
  "visibility": "private",
  "isTemplate": false
}
```

#### 获取项目列表
```http
GET /api/projects
```

#### 获取我的项目
```http
GET /api/projects/my
```

#### 获取项目详情
```http
GET /api/projects/{id}
```

#### 更新项目
```http
PATCH /api/projects/{id}
Content-Type: application/json

{
  "name": "更新的项目名称",
  "description": "更新的描述"
}
```

#### 删除项目
```http
DELETE /api/projects/{id}
```

### 项目成员管理

#### 添加项目成员
```http
POST /api/projects/{id}/members
Content-Type: application/json

{
  "userId": "user-id",
  "role": "editor"
}
```

#### 更新成员角色
```http
PATCH /api/projects/{id}/members/{memberId}
Content-Type: application/json

{
  "role": "admin"
}
```

#### 删除项目成员
```http
DELETE /api/projects/{id}/members/{memberId}
```

### 场景管理

#### 创建场景
```http
POST /api/projects/{projectId}/scenes
Content-Type: application/json

{
  "name": "新场景",
  "description": "场景描述",
  "isDefault": false
}
```

#### 获取项目场景
```http
GET /api/projects/{projectId}/scenes
```

#### 获取场景详情
```http
GET /api/scenes/{id}
```

#### 更新场景
```http
PATCH /api/scenes/{id}
Content-Type: application/json

{
  "name": "更新的场景名称"
}
```

#### 删除场景
```http
DELETE /api/scenes/{id}
```

### 场景实体管理

#### 添加场景实体
```http
POST /api/scenes/{sceneId}/entities
Content-Type: application/json

{
  "name": "立方体",
  "type": "Mesh",
  "transform": {
    "position": [0, 0, 0],
    "rotation": [0, 0, 0],
    "scale": [1, 1, 1]
  },
  "properties": {
    "geometry": "box",
    "material": "standard"
  }
}
```

## 数据模型

### 项目 (Project)
- `id`: 项目唯一标识
- `name`: 项目名称
- `description`: 项目描述
- `thumbnailUrl`: 缩略图URL
- `visibility`: 可见性 (public/private)
- `ownerId`: 项目所有者ID
- `isTemplate`: 是否为模板
- `isArchived`: 是否已归档

### 项目成员 (ProjectMember)
- `id`: 成员记录ID
- `userId`: 用户ID
- `projectId`: 项目ID
- `role`: 角色 (owner/admin/editor/viewer)

### 场景 (Scene)
- `id`: 场景唯一标识
- `name`: 场景名称
- `description`: 场景描述
- `projectId`: 所属项目ID
- `isDefault`: 是否为默认场景
- `metadata`: 场景元数据

### 场景实体 (SceneEntity)
- `id`: 实体唯一标识
- `sceneId`: 所属场景ID
- `name`: 实体名称
- `type`: 实体类型
- `transform`: 变换信息
- `properties`: 实体属性
- `parentId`: 父实体ID

## 开发指南

### 项目结构
```
src/
├── auth/                 # 认证模块
│   ├── guards/          # 认证守卫
│   ├── strategies/      # 认证策略
│   └── decorators/      # 装饰器
├── common/              # 公共模块
│   ├── filters/         # 异常过滤器
│   └── interceptors/    # 拦截器
├── projects/            # 项目模块
│   ├── dto/            # 数据传输对象
│   ├── entities/       # 实体定义
│   ├── projects.controller.ts
│   ├── projects.service.ts
│   └── projects.module.ts
├── scenes/              # 场景模块
│   ├── dto/
│   ├── entities/
│   ├── scenes.controller.ts
│   ├── scenes.service.ts
│   └── scenes.module.ts
├── health/              # 健康检查模块
├── app.module.ts        # 应用主模块
└── main.ts             # 应用入口
```

### 测试
```bash
# 单元测试
npm run test

# 端到端测试
npm run test:e2e

# 测试覆盖率
npm run test:cov
```

### 代码质量
```bash
# 代码检查
npm run lint

# 代码格式化
npm run format
```

## 部署

### Docker部署
```bash
# 构建镜像
docker build -t project-service .

# 运行容器
docker run -p 4002:4002 -p 3002:3002 project-service
```

### 环境变量
生产环境需要配置以下环境变量：
- `NODE_ENV=production`
- `DB_HOST`, `DB_PORT`, `DB_USERNAME`, `DB_PASSWORD`, `DB_DATABASE`
- `JWT_SECRET`
- `PROJECT_SERVICE_HOST`, `PROJECT_SERVICE_PORT`, `PROJECT_SERVICE_HTTP_PORT`

## 监控和维护

### 健康检查
```http
GET /api/health
```

### API文档
启动服务后访问：
```
http://localhost:4002/api/docs
```

### 日志
日志文件位置：`./logs/project-service.log`

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
