// 简单测试文件
console.log('开始测试...');

try {
  console.log('测试 Node.js 基础功能...');
  console.log('Node.js 版本:', process.version);
  console.log('当前目录:', process.cwd());
  
  console.log('测试 NestJS 导入...');
  const { NestFactory } = require('@nestjs/core');
  console.log('NestJS 导入成功');
  
  console.log('测试 TypeORM 导入...');
  const typeorm = require('typeorm');
  console.log('TypeORM 导入成功');
  
  console.log('测试 TensorFlow.js 导入...');
  const tf = require('@tensorflow/tfjs');
  console.log('TensorFlow.js 导入成功');
  console.log('TensorFlow.js 版本:', tf.version.tfjs);
  
  console.log('所有基础模块测试通过！');
} catch (error) {
  console.error('测试失败:', error.message);
  console.error('错误堆栈:', error.stack);
}
