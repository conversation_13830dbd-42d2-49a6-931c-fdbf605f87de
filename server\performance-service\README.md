# 性能优化服务 (Performance Service)

## 概述

性能优化服务是DL引擎生态系统中的核心微服务，专门负责分布式环境下的性能监控、自动调优和负载均衡优化。该服务提供实时性能指标收集、智能优化建议生成、自动化性能调优等功能，确保整个系统的高效运行。

## 主要功能

### 🔍 核心功能
- **实时性能监控**: 收集和分析CPU、内存、网络、应用层面的性能指标
- **自动性能调优**: 基于性能数据自动调整系统参数和配置
- **负载均衡优化**: 智能分配请求负载，优化资源利用率
- **性能瓶颈识别**: 自动识别系统性能瓶颈并提供优化建议
- **资源使用优化**: 优化内存、CPU、网络等系统资源的使用效率

### 🚀 高级特性
- **分布式监控**: 支持多节点分布式环境的统一性能监控
- **智能告警**: 基于阈值和趋势的智能性能告警系统
- **历史数据分析**: 性能数据的长期存储和趋势分析
- **API接口**: 完整的RESTful API接口，支持第三方集成
- **实时仪表板**: 提供实时性能监控仪表板

## 技术架构

### 🏗️ 技术栈
- **框架**: NestJS 10.x
- **语言**: TypeScript 5.x
- **数据库**: PostgreSQL (性能数据存储)
- **缓存**: Redis (实时数据缓存和消息队列)
- **事件**: EventEmitter2 (事件驱动架构)
- **调度**: @nestjs/schedule (定时任务)
- **认证**: JWT (API认证)
- **文档**: Swagger/OpenAPI
- **测试**: Jest (单元测试和集成测试)

### 📊 监控指标
- **CPU指标** - 使用率、负载平均值、核心数
- **内存指标** - 使用量、使用率、堆内存统计
- **网络指标** - 流入/流出字节数、活跃连接数
- **应用指标** - 请求/秒、平均响应时间、错误率
- **优化指标** - 缓存命中率、连接池利用率、并行效率

## 快速开始

### 环境要求
- Node.js >= 18.0.0
- PostgreSQL >= 12.0.0
- Redis >= 6.0.0
- npm >= 8.0.0
- TypeScript >= 5.0.0

### 安装依赖
```bash
npm install
```

### 环境配置
复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的参数：
```env
# 基础配置
NODE_ENV=development
PORT=3060
HOST=0.0.0.0

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=performance_user
DB_PASSWORD=performance_password
DB_DATABASE=performance_db

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-here
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod

# 使用Docker
docker-compose up -d

# 开发环境Docker
docker-compose -f docker-compose.dev.yml up -d
```

## API 接口

### 性能监控
```http
# 获取性能指标
GET /api/v1/performance/metrics?nodeId=node1&limit=100

# 获取实时性能指标
GET /api/v1/performance/metrics/realtime/{nodeId}

# 获取性能统计摘要
GET /api/v1/performance/metrics/summary/{nodeId}?period=1h
```

### 优化管理
```http
# 获取优化建议
GET /api/v1/performance/recommendations/{nodeId}

# 获取优化配置
GET /api/v1/performance/config/{nodeId}

# 更新优化配置
PUT /api/v1/performance/config/{nodeId}
Content-Type: application/json

{
  "enableAutoTuning": true,
  "maxMemoryUsage": 85,
  "maxCpuUsage": 80
}
```

### 健康检查
```http
# 基础健康检查
GET /health

# 详细健康检查
GET /health/detailed

# 就绪检查
GET /health/ready

# 存活检查
GET /health/live
```

## 配置说明

### 性能监控配置
- `ENABLE_PERFORMANCE_MONITORING`: 是否启用性能监控
- `PERFORMANCE_METRICS_INTERVAL`: 性能指标收集间隔(毫秒)
- `ENABLE_AUTO_TUNING`: 是否启用自动调优
- `ENABLE_LOAD_BALANCING`: 是否启用负载均衡

### 告警配置
- `ALERT_CPU_THRESHOLD`: CPU使用率告警阈值(%)
- `ALERT_MEMORY_THRESHOLD`: 内存使用率告警阈值(%)
- `ALERT_RESPONSE_TIME_THRESHOLD`: 响应时间告警阈值(毫秒)
- `ALERT_ERROR_RATE_THRESHOLD`: 错误率告警阈值(%)

## 开发指南

### 项目结构
```
src/
├── controllers/          # 控制器
├── services/            # 服务层
├── modules/             # 模块定义
├── dto/                 # 数据传输对象
├── entities/            # 数据库实体
├── guards/              # 守卫
├── interceptors/        # 拦截器
├── decorators/          # 装饰器
├── config/              # 配置文件
├── constants/           # 常量定义
└── utils/               # 工具函数
```

### 运行测试
```bash
# 单元测试
npm run test

# 监听模式测试
npm run test:watch

# 覆盖率测试
npm run test:cov

# 集成测试
npm run test:e2e
```

### 代码规范
```bash
# 代码格式化
npm run format

# 代码检查
npm run lint
```

## 部署指南

### Docker部署
```bash
# 构建镜像
docker build -t performance-service .

# 运行容器
docker run -p 3060:3060 -p 3061:3061 performance-service

# 使用Docker Compose
docker-compose up -d
```

### 生产环境部署
1. 确保数据库和Redis服务可用
2. 配置环境变量
3. 构建生产版本
4. 启动服务
5. 配置反向代理(可选)
6. 设置监控和日志

## 监控和运维

### 健康检查端点
- `/health` - 基础健康检查
- `/health/detailed` - 详细健康信息
- `/health/ready` - 服务就绪状态
- `/health/live` - 服务存活状态

### 日志配置
服务支持结构化日志输出，可配置日志级别和格式：
```env
LOG_LEVEL=info
LOG_FORMAT=json
```

### 性能指标
服务会自动收集和暴露以下性能指标：
- 请求处理时间
- 内存使用情况
- CPU使用率
- 错误率统计

## 故障排除

### 常见问题
1. **服务启动失败**: 检查数据库和Redis连接配置
2. **性能数据缺失**: 确认性能监控已启用
3. **认证失败**: 检查JWT密钥配置
4. **内存使用过高**: 调整缓存大小和GC参数

### 日志分析
查看服务日志以诊断问题：
```bash
# Docker环境
docker logs performance-service

# 本地环境
tail -f logs/application.log
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

- 项目维护者: DL Engine Team
- 邮箱: <EMAIL>
- 文档: [API文档](http://localhost:3060/api/docs)
