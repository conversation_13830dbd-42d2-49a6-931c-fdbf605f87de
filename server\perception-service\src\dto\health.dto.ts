/**
 * 健康检查DTO
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 健康状态数据
 */
export class HealthStatusDto {
  @ApiProperty({ description: '服务是否健康' })
  healthy: boolean;

  @ApiProperty({ description: '服务状态' })
  status: string;

  @ApiProperty({ description: '检查时间' })
  timestamp: string;

  @ApiProperty({ description: '服务版本' })
  version: string;

  @ApiProperty({ description: '运行时间(秒)' })
  uptime: number;

  @ApiPropertyOptional({ description: '内存使用情况' })
  memory?: {
    used: number;
    total: number;
    percentage: number;
  };

  @ApiPropertyOptional({ description: 'CPU使用情况' })
  cpu?: {
    usage: number;
    loadAverage: number[];
  };

  @ApiPropertyOptional({ description: '依赖服务状态' })
  dependencies?: {
    redis: {
      status: string;
      latency?: number;
      error?: string;
    };
    engine: {
      status: string;
      error?: string;
    };
  };

  @ApiPropertyOptional({ description: '服务指标' })
  metrics?: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    queueSize: number;
    processingRate: number;
  };

  @ApiPropertyOptional({ description: '错误信息' })
  errors?: string[];
}

/**
 * 健康检查响应DTO
 */
export class HealthResponseDto {
  @ApiProperty({ description: '操作是否成功' })
  success: boolean;

  @ApiProperty({ description: '响应消息' })
  message: string;

  @ApiProperty({ description: '健康状态数据', type: HealthStatusDto })
  data: HealthStatusDto;
}
