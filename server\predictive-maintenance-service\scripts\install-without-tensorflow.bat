@echo off
echo 安装不包含 TensorFlow 原生模块的依赖...

REM 删除现有的 node_modules
if exist node_modules (
    echo 删除现有 node_modules...
    rmdir /s /q node_modules
)

REM 删除 package-lock.json
if exist package-lock.json (
    del package-lock.json
)

REM 清理缓存
npm cache clean --force

REM 设置环境变量跳过原生模块编译
set npm_config_build_from_source=false
set npm_config_cache_min=999999999

REM 安装依赖，跳过可选依赖
echo 安装依赖（跳过可选依赖）...
npm install --no-optional --legacy-peer-deps

echo 安装完成！
echo 注意：已跳过 TensorFlow 原生模块，使用浏览器版本
pause
