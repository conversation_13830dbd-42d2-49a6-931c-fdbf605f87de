/**
 * 数据库种子数据脚本
 */
const mysql = require('mysql2/promise');
const { v4: uuidv4 } = require('uuid');

async function seedData() {
  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || 'password',
    database: 'ir_engine_projects',
  };

  console.log('🌱 开始插入种子数据...');

  try {
    const connection = await mysql.createConnection(config);
    console.log('✅ 数据库连接成功');

    // 清理现有数据（可选）
    if (process.env.CLEAR_DATA === 'true') {
      console.log('🗑️ 清理现有数据...');
      await connection.execute('DELETE FROM scene_entities');
      await connection.execute('DELETE FROM scenes');
      await connection.execute('DELETE FROM project_settings');
      await connection.execute('DELETE FROM project_members');
      await connection.execute('DELETE FROM projects');
    }

    // 插入示例项目
    const projects = [
      {
        id: uuidv4(),
        name: '演示项目 - 虚拟展厅',
        description: '一个展示3D虚拟展厅的演示项目',
        ownerId: 'demo-user-1',
        visibility: 'public',
        isTemplate: false,
      },
      {
        id: uuidv4(),
        name: '教育项目 - 太阳系模拟',
        description: '太阳系行星运动的3D模拟项目',
        ownerId: 'demo-user-2',
        visibility: 'public',
        isTemplate: false,
      },
      {
        id: uuidv4(),
        name: '建筑可视化模板',
        description: '用于建筑可视化的项目模板',
        ownerId: 'system',
        visibility: 'public',
        isTemplate: true,
      },
    ];

    console.log('📦 插入示例项目...');
    for (const project of projects) {
      await connection.execute(
        'INSERT INTO projects (id, name, description, ownerId, visibility, isTemplate) VALUES (?, ?, ?, ?, ?, ?)',
        [project.id, project.name, project.description, project.ownerId, project.visibility, project.isTemplate]
      );

      // 为每个项目创建默认场景
      const sceneId = uuidv4();
      await connection.execute(
        'INSERT INTO scenes (id, name, description, projectId, isDefault) VALUES (?, ?, ?, ?, ?)',
        [sceneId, '主场景', '项目的主要场景', project.id, true]
      );

      // 为场景添加一些基本实体
      const entities = [
        {
          id: uuidv4(),
          sceneId,
          name: '主摄像机',
          type: 'Camera',
          transform: JSON.stringify({ position: [0, 1.6, 5], rotation: [0, 0, 0], scale: [1, 1, 1] }),
          properties: JSON.stringify({ fov: 60, near: 0.1, far: 1000 }),
        },
        {
          id: uuidv4(),
          sceneId,
          name: '方向光',
          type: 'DirectionalLight',
          transform: JSON.stringify({ position: [5, 10, 5], rotation: [-45, 45, 0], scale: [1, 1, 1] }),
          properties: JSON.stringify({ intensity: 1, color: '#ffffff', castShadow: true }),
        },
        {
          id: uuidv4(),
          sceneId,
          name: '地面',
          type: 'Plane',
          transform: JSON.stringify({ position: [0, 0, 0], rotation: [-90, 0, 0], scale: [10, 10, 1] }),
          properties: JSON.stringify({ material: 'standard', color: '#808080' }),
        },
      ];

      for (const entity of entities) {
        await connection.execute(
          'INSERT INTO scene_entities (id, sceneId, name, type, transform, properties) VALUES (?, ?, ?, ?, ?, ?)',
          [entity.id, entity.sceneId, entity.name, entity.type, entity.transform, entity.properties]
        );
      }

      // 添加项目设置
      const settings = [
        {
          id: uuidv4(),
          projectId: project.id,
          key: 'renderQuality',
          value: 'high',
          type: 'string',
          description: '渲染质量设置',
        },
        {
          id: uuidv4(),
          projectId: project.id,
          key: 'enableShadows',
          value: 'true',
          type: 'boolean',
          description: '是否启用阴影',
        },
        {
          id: uuidv4(),
          projectId: project.id,
          key: 'maxFPS',
          value: '60',
          type: 'number',
          description: '最大帧率',
        },
      ];

      for (const setting of settings) {
        await connection.execute(
          'INSERT INTO project_settings (id, projectId, `key`, value, type, description) VALUES (?, ?, ?, ?, ?, ?)',
          [setting.id, setting.projectId, setting.key, setting.value, setting.type, setting.description]
        );
      }

      console.log(`  ✅ 项目 "${project.name}" 创建完成`);
    }

    await connection.end();
    console.log('🎉 种子数据插入完成！');
  } catch (error) {
    console.error('❌ 种子数据插入失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  seedData();
}

module.exports = { seedData };
