/**
 * 端到端测试
 */
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { ValidationPipe } from '@nestjs/common';

describe('ProjectService (e2e)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    
    // 配置全局管道
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
      }),
    );

    await app.init();

    // 模拟获取认证token
    authToken = 'Bearer mock-jwt-token';
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/api/projects (GET)', () => {
    it('should return 401 without auth token', () => {
      return request(app.getHttpServer())
        .get('/api/projects')
        .expect(401);
    });

    it('should return projects with auth token', () => {
      return request(app.getHttpServer())
        .get('/api/projects')
        .set('Authorization', authToken)
        .expect(200);
    });
  });

  describe('/api/projects (POST)', () => {
    it('should create a project', () => {
      const createProjectDto = {
        name: '测试项目',
        description: '这是一个测试项目',
        visibility: 'private',
      };

      return request(app.getHttpServer())
        .post('/api/projects')
        .set('Authorization', authToken)
        .send(createProjectDto)
        .expect(201);
    });

    it('should return 400 for invalid data', () => {
      const invalidDto = {
        // 缺少必需的name字段
        description: '无效的项目数据',
      };

      return request(app.getHttpServer())
        .post('/api/projects')
        .set('Authorization', authToken)
        .send(invalidDto)
        .expect(400);
    });
  });

  describe('/api/projects/my (GET)', () => {
    it('should return user projects', () => {
      return request(app.getHttpServer())
        .get('/api/projects/my')
        .set('Authorization', authToken)
        .expect(200);
    });
  });

  describe('/api/projects/:id (GET)', () => {
    it('should return a specific project', () => {
      const projectId = 'test-project-id';
      
      return request(app.getHttpServer())
        .get(`/api/projects/${projectId}`)
        .set('Authorization', authToken)
        .expect(200);
    });

    it('should return 404 for non-existent project', () => {
      const nonExistentId = 'non-existent-id';
      
      return request(app.getHttpServer())
        .get(`/api/projects/${nonExistentId}`)
        .set('Authorization', authToken)
        .expect(404);
    });
  });

  describe('/api/projects/:id (PATCH)', () => {
    it('should update a project', () => {
      const projectId = 'test-project-id';
      const updateDto = {
        name: '更新的项目名称',
        description: '更新的项目描述',
      };

      return request(app.getHttpServer())
        .patch(`/api/projects/${projectId}`)
        .set('Authorization', authToken)
        .send(updateDto)
        .expect(200);
    });
  });

  describe('/api/projects/:id (DELETE)', () => {
    it('should delete a project', () => {
      const projectId = 'test-project-id';
      
      return request(app.getHttpServer())
        .delete(`/api/projects/${projectId}`)
        .set('Authorization', authToken)
        .expect(204);
    });
  });

  describe('/api/health (GET)', () => {
    it('should return health status', () => {
      return request(app.getHttpServer())
        .get('/api/health')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status');
          expect(res.body.status).toBe('ok');
        });
    });
  });
});
