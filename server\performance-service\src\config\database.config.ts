/**
 * 数据库配置
 */

import { registerAs } from '@nestjs/config';

export default registerAs('database', () => ({
  type: process.env.DB_TYPE || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT, 10) || 5432,
  username: process.env.DB_USERNAME || 'performance_user',
  password: process.env.DB_PASSWORD || 'performance_password',
  database: process.env.DB_DATABASE || 'performance_db',
  synchronize: process.env.DB_SYNCHRONIZE === 'true',
  logging: process.env.DB_LOGGING === 'true',
  retryAttempts: parseInt(process.env.DB_RETRY_ATTEMPTS, 10) || 3,
  retryDelay: parseInt(process.env.DB_RETRY_DELAY, 10) || 3000,
  autoLoadEntities: true,
  ssl:
    process.env.DB_SSL === 'true'
      ? {
          rejectUnauthorized: false,
        }
      : false,
  extra: {
    connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT, 10) || 10,
    acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT, 10) || 60000,
    timeout: parseInt(process.env.DB_TIMEOUT, 10) || 60000,
  },
}));
