# 感知数据处理服务 Dockerfile

# 使用官方Node.js运行时作为基础镜像
FROM node:18-alpine AS base

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# 复制package文件
COPY package*.json ./
COPY tsconfig.json ./
COPY nest-cli.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 开发阶段
FROM base AS development

# 安装开发依赖
RUN npm ci

# 复制源代码
COPY src/ ./src/

# 暴露端口
EXPOSE 3050

# 启动开发服务器
CMD ["npm", "run", "start:dev"]

# 构建阶段
FROM base AS build

# 安装所有依赖
RUN npm ci

# 复制源代码
COPY src/ ./src/

# 构建应用
RUN npm run build

# 生产阶段
FROM node:18-alpine AS production

# 设置工作目录
WORKDIR /app

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nestjs -u 1001

# 复制package文件
COPY package*.json ./

# 安装生产依赖
RUN npm ci --only=production && npm cache clean --force

# 复制构建产物
COPY --from=build /app/dist ./dist

# 创建日志目录
RUN mkdir -p logs && chown -R nestjs:nodejs logs

# 切换到非root用户
USER nestjs

# 暴露端口
EXPOSE 3050

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3050/api/v1/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 启动应用
CMD ["node", "dist/main.js"]
