/**
 * 异常检测DTO
 */

import { <PERSON>S<PERSON>, Is<PERSON>ptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class DetectAnomaliesDto {
  @ApiProperty({ 
    description: '设备ID', 
    example: 'device-001',
    minLength: 1,
    maxLength: 50
  })
  @IsString()
  deviceId: string;

  @ApiPropertyOptional({ 
    description: '检测敏感度（0-1之间，值越高越敏感）', 
    example: 0.95,
    minimum: 0.1,
    maximum: 1.0,
    default: 0.95
  })
  @IsOptional()
  @IsNumber()
  @Min(0.1)
  @Max(1.0)
  sensitivity?: number = 0.95;
}
