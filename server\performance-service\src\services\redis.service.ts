/**
 * Redis服务
 */

import { Injectable, Logger, Inject, OnModuleDestroy } from '@nestjs/common';
import Redis from 'ioredis';

@Injectable()
export class RedisService implements OnModuleDestroy {
  private readonly logger = new Logger(RedisService.name);
  private readonly redis: Redis;

  constructor(@Inject('REDIS_CONFIG') private readonly redisConfig: any) {
    this.redis = new Redis(redisConfig);

    this.redis.on('connect', () => {
      this.logger.log('Redis连接已建立');
    });

    this.redis.on('error', (error) => {
      this.logger.error('Redis连接错误:', error);
    });

    this.redis.on('close', () => {
      this.logger.warn('Redis连接已关闭');
    });
  }

  async onModuleDestroy() {
    await this.redis.quit();
  }

  getClient(): Redis {
    return this.redis;
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    const serializedValue = JSON.stringify(value);
    if (ttl) {
      await this.redis.setex(key, ttl, serializedValue);
    } else {
      await this.redis.set(key, serializedValue);
    }
  }

  async get<T>(key: string): Promise<T | null> {
    const value = await this.redis.get(key);
    return value ? JSON.parse(value) : null;
  }

  async del(key: string): Promise<void> {
    await this.redis.del(key);
  }

  async exists(key: string): Promise<boolean> {
    const result = await this.redis.exists(key);
    return result === 1;
  }

  async keys(pattern: string): Promise<string[]> {
    return await this.redis.keys(pattern);
  }

  async hset(key: string, field: string, value: any): Promise<void> {
    await this.redis.hset(key, field, JSON.stringify(value));
  }

  async hget<T>(key: string, field: string): Promise<T | null> {
    const value = await this.redis.hget(key, field);
    return value ? JSON.parse(value) : null;
  }

  async hgetall<T>(key: string): Promise<Record<string, T>> {
    const result = await this.redis.hgetall(key);
    const parsed: Record<string, T> = {};

    for (const [field, value] of Object.entries(result)) {
      parsed[field] = JSON.parse(value);
    }

    return parsed;
  }

  async hdel(key: string, field: string): Promise<void> {
    await this.redis.hdel(key, field);
  }

  async lpush(key: string, value: any): Promise<void> {
    await this.redis.lpush(key, JSON.stringify(value));
  }

  async rpush(key: string, value: any): Promise<void> {
    await this.redis.rpush(key, JSON.stringify(value));
  }

  async lpop<T>(key: string): Promise<T | null> {
    const value = await this.redis.lpop(key);
    return value ? JSON.parse(value) : null;
  }

  async rpop<T>(key: string): Promise<T | null> {
    const value = await this.redis.rpop(key);
    return value ? JSON.parse(value) : null;
  }

  async lrange<T>(key: string, start: number, stop: number): Promise<T[]> {
    const values = await this.redis.lrange(key, start, stop);
    return values.map((value) => JSON.parse(value));
  }

  async llen(key: string): Promise<number> {
    return await this.redis.llen(key);
  }

  async ltrim(key: string, start: number, stop: number): Promise<void> {
    await this.redis.ltrim(key, start, stop);
  }

  async expire(key: string, seconds: number): Promise<void> {
    await this.redis.expire(key, seconds);
  }

  async ttl(key: string): Promise<number> {
    return await this.redis.ttl(key);
  }

  async ping(): Promise<string> {
    return await this.redis.ping();
  }

  async flushdb(): Promise<void> {
    await this.redis.flushdb();
  }

  async info(): Promise<string> {
    return await this.redis.info();
  }
}
