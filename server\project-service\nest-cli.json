{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "webpack": false, "tsConfigPath": "tsconfig.json", "assets": ["**/*.json", "**/*.md", "**/*.txt"], "watchAssets": true}, "projects": {"project-service": {"type": "application", "root": "", "entryFile": "main", "sourceRoot": "src", "compilerOptions": {"tsConfigPath": "tsconfig.json"}}}, "generateOptions": {"spec": true, "flat": false}, "monorepo": false, "root": "src"}