/**
 * NLP场景生成服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GeneratedScene } from './entities/generated-scene.entity';
import { SceneTemplate } from './entities/scene-template.entity';
import { AIModelService } from './ai-model.service';
import { SceneStorageService } from './scene-storage.service';
import {
  GenerateSceneDto, PreviewSceneDto,
  SaveSceneDto, GenerateSceneResponse
} from './dto/nlp-scene.dto';

@Injectable()
export class NLPSceneService {
  private readonly logger = new Logger(NLPSceneService.name);

  constructor(
    @InjectRepository(GeneratedScene)
    private generatedSceneRepository: Repository<GeneratedScene>,
    @InjectRepository(SceneTemplate)
    private sceneTemplateRepository: Repository<SceneTemplate>,
    private aiModelService: AIModelService,
    private sceneStorageService: SceneStorageService
  ) {}

  /**
   * 生成场景
   */
  async generateScene(generateDto: GenerateSceneDto): Promise<GenerateSceneResponse> {
    const startTime = Date.now();

    try {
      this.logger.log(`开始生成场景: ${generateDto.text}`);

      // 1. 理解自然语言文本
      const understanding = await this.aiModelService.understandText(generateDto.text);

      // 2. 规划场景结构
      const scenePlan = await this.aiModelService.planScene(understanding, {
        style: generateDto.style,
        quality: generateDto.quality,
        maxObjects: generateDto.maxObjects,
        constraints: generateDto.constraints
      });

      // 3. 生成场景内容
      const sceneData = await this.aiModelService.generateSceneContent(scenePlan);

      // 4. 计算元数据
      const generationTime = Date.now() - startTime;
      const metadata = {
        generationTime,
        objectCount: sceneData.entities?.length || 0,
        polygonCount: this.calculatePolygonCount(sceneData),
        complexity: this.calculateSceneComplexity(sceneData),
        customFeaturesUsed: {
          customStyle: !!generateDto.customStyle,
          customObjects: !!generateDto.customObjects,
          aiServices: generateDto.aiServices?.length || 0,
          advancedFeatures: !!generateDto.enableAdvancedFeatures
        }
      };

      // 5. 保存到数据库
      const generatedScene = this.generatedSceneRepository.create({
        inputText: generateDto.text,
        style: generateDto.style,
        quality: generateDto.quality,
        maxObjects: generateDto.maxObjects,
        userId: generateDto.userId,
        projectId: generateDto.projectId,
        sceneData,
        understanding,
        objectCount: metadata.objectCount,
        polygonCount: metadata.polygonCount,
        generationTime: metadata.generationTime,
        metadata
      });

      const savedScene = await this.generatedSceneRepository.save(generatedScene);

      // 6. 存储场景文件
      const sceneUrl = await this.sceneStorageService.saveSceneFile(
        savedScene.id,
        sceneData
      );

      // 更新场景URL
      savedScene.sceneUrl = sceneUrl;
      await this.generatedSceneRepository.save(savedScene);

      this.logger.log(`场景生成完成: ${savedScene.id}, 耗时: ${generationTime}ms`);

      return {
        success: true,
        sceneId: savedScene.id,
        sceneUrl,
        sceneData,
        understanding,
        metadata
      };

    } catch (error) {
      this.logger.error(`场景生成失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 预览场景
   */
  async previewScene(previewDto: PreviewSceneDto): Promise<any> {
    try {
      this.logger.log(`开始预览场景: ${previewDto.text}`);

      // 使用低质量设置进行快速预览
      const understanding = await this.aiModelService.understandText(previewDto.text);

      const previewPlan = await this.aiModelService.planScene(understanding, {
        style: previewDto.style,
        quality: previewDto.lowQuality ? 30 : 60,
        maxObjects: 20,
        constraints: {
          maxPolygons: 10000,
          targetFrameRate: 30
        }
      });

      const previewData = await this.aiModelService.generatePreview(previewPlan);

      return {
        previewData,
        understanding,
        estimatedObjects: previewPlan.objects?.length || 0
      };

    } catch (error) {
      this.logger.error(`场景预览失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取生成历史
   */
  async getGenerationHistory(
    userId: string,
    page: number = 1,
    limit: number = 10,
    style?: string
  ): Promise<any> {
    const queryBuilder = this.generatedSceneRepository
      .createQueryBuilder('scene')
      .where('scene.userId = :userId', { userId })
      .orderBy('scene.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    if (style) {
      queryBuilder.andWhere('scene.style = :style', { style });
    }

    const [scenes, total] = await queryBuilder.getManyAndCount();

    return {
      scenes: scenes.map(scene => ({
        id: scene.id,
        inputText: scene.inputText,
        style: scene.style,
        quality: scene.quality,
        objectCount: scene.objectCount,
        generationTime: scene.generationTime,
        createdAt: scene.createdAt,
        sceneUrl: scene.sceneUrl
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * 保存生成的场景
   */
  async saveGeneratedScene(saveDto: SaveSceneDto): Promise<any> {
    try {
      const sceneUrl = await this.sceneStorageService.saveSceneFile(
        `user_${saveDto.userId}_${Date.now()}`,
        saveDto.sceneData
      );

      const savedScene = this.generatedSceneRepository.create({
        inputText: saveDto.description || saveDto.name,
        style: 'custom',
        quality: 100,
        maxObjects: 0,
        userId: saveDto.userId,
        projectId: saveDto.projectId,
        sceneData: saveDto.sceneData,
        sceneUrl,
        objectCount: saveDto.sceneData.entities?.length || 0,
        polygonCount: this.calculatePolygonCount(saveDto.sceneData),
        metadata: {
          name: saveDto.name,
          description: saveDto.description,
          tags: saveDto.tags,
          savedAt: new Date()
        }
      });

      const result = await this.generatedSceneRepository.save(savedScene);

      return {
        sceneId: result.id,
        sceneUrl: result.sceneUrl
      };

    } catch (error) {
      this.logger.error(`保存场景失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取场景模板
   */
  async getSceneTemplates(category?: string, style?: string): Promise<SceneTemplate[]> {
    const queryBuilder = this.sceneTemplateRepository
      .createQueryBuilder('template')
      .where('template.isActive = :isActive', { isActive: true })
      .orderBy('template.popularity', 'DESC');

    if (category) {
      queryBuilder.andWhere('template.category = :category', { category });
    }

    if (style) {
      queryBuilder.andWhere('template.style = :style', { style });
    }

    return await queryBuilder.getMany();
  }

  /**
   * 获取用户统计
   */
  async getUserStats(userId: string): Promise<any> {
    const totalScenes = await this.generatedSceneRepository.count({
      where: { userId }
    });

    const styleStats = await this.generatedSceneRepository
      .createQueryBuilder('scene')
      .select('scene.style', 'style')
      .addSelect('COUNT(*)', 'count')
      .where('scene.userId = :userId', { userId })
      .groupBy('scene.style')
      .getRawMany();

    const avgGenerationTime = await this.generatedSceneRepository
      .createQueryBuilder('scene')
      .select('AVG(scene.generationTime)', 'avgTime')
      .where('scene.userId = :userId', { userId })
      .getRawOne();

    return {
      totalScenes,
      styleStats,
      avgGenerationTime: avgGenerationTime?.avgTime || 0
    };
  }

  /**
   * 计算多边形数量
   */
  private calculatePolygonCount(sceneData: any): number {
    let totalPolygons = 0;

    if (sceneData.entities) {
      sceneData.entities.forEach((entity: any) => {
        // 根据对象类型估算多边形数量
        const basePolygons = this.getBasePolygonCount(entity.type);
        const complexityMultiplier = entity.complexity || 1;
        totalPolygons += basePolygons * complexityMultiplier;
      });
    }

    return totalPolygons;
  }

  /**
   * 获取基础多边形数量
   */
  private getBasePolygonCount(objectType: string): number {
    const polygonMap: { [key: string]: number } = {
      '桌子': 12,
      '椅子': 24,
      '沙发': 48,
      '电脑': 16,
      '植物': 32,
      '灯': 20,
      '汽车': 200,
      '建筑': 100,
      '房子': 150
    };

    return polygonMap[objectType] || 20;
  }

  /**
   * 计算场景复杂度
   */
  private calculateSceneComplexity(sceneData: any): number {
    let complexity = 0;

    if (sceneData.entities) {
      sceneData.entities.forEach((entity: any) => {
        complexity += entity.complexity || 1;
      });
    }

    // 基于对象数量的复杂度加成
    const entityCount = sceneData.entities?.length || 0;
    complexity += Math.log(entityCount + 1) * 2;

    return Math.round(complexity * 10) / 10;
  }
}
