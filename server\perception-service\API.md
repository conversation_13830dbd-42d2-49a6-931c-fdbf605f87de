# 感知数据处理服务 API 文档

## 概述

感知数据处理服务提供RESTful API接口，用于处理和管理大规模感知数据。所有API响应都遵循统一的格式。

## 基础信息

- **基础URL**: `http://localhost:3050/api/v1`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

## 感知数据处理接口

### 1. 处理单个感知数据

**接口**: `POST /perception/process`

**描述**: 处理单个感知数据，支持多种感知模态

**请求体**:
```json
{
  "id": "perception_001",
  "entityId": "entity_001",
  "sessionId": "session_001",
  "modality": "VISUAL",
  "timestamp": 1640995200000,
  "confidence": 0.85,
  "data": {
    "objects": [
      {
        "id": "obj_001",
        "type": "person",
        "position": { "x": 10, "y": 5, "z": 0 },
        "confidence": 0.9
      }
    ],
    "lighting": {
      "intensity": 0.7,
      "direction": { "x": 1, "y": -1, "z": 0 }
    }
  },
  "metadata": {
    "source": "camera_001",
    "resolution": "1920x1080"
  }
}
```

**响应**:
```json
{
  "success": true,
  "message": "感知数据处理成功",
  "data": {
    "id": "perception_001",
    "entityId": "entity_001",
    "processed": true,
    "timestamp": 1640995200000
  }
}
```

### 2. 批量处理感知数据

**接口**: `POST /perception/process/batch`

**描述**: 批量处理多个感知数据

**请求体**:
```json
{
  "data": [
    {
      "id": "perception_001",
      "entityId": "entity_001",
      "sessionId": "session_001",
      "modality": "VISUAL",
      "timestamp": 1640995200000,
      "confidence": 0.85,
      "data": { ... }
    },
    {
      "id": "perception_002",
      "entityId": "entity_001",
      "sessionId": "session_001",
      "modality": "AUDITORY",
      "timestamp": 1640995201000,
      "confidence": 0.78,
      "data": { ... }
    }
  ]
}
```

**响应**:
```json
{
  "success": true,
  "message": "批量处理完成",
  "data": {
    "total": 2,
    "successful": 2,
    "failed": 0,
    "results": [
      {
        "index": 0,
        "status": "fulfilled",
        "error": null
      },
      {
        "index": 1,
        "status": "fulfilled",
        "error": null
      }
    ]
  }
}
```

### 3. 获取实体感知数据

**接口**: `GET /perception/entity/{entityId}/data`

**参数**:
- `entityId` (路径参数): 实体ID
- `limit` (查询参数): 数据条数限制，默认10

**示例**: `GET /perception/entity/entity_001/data?limit=5`

**响应**:
```json
{
  "success": true,
  "message": "获取实体感知数据成功",
  "data": [
    {
      "id": "perception_001",
      "entityId": "entity_001",
      "modality": "VISUAL",
      "timestamp": 1640995200000,
      "confidence": 0.85,
      "processed": true,
      "anomalies": []
    }
  ]
}
```

### 4. 获取实体融合数据

**接口**: `GET /perception/entity/{entityId}/fused`

**参数**:
- `entityId` (路径参数): 实体ID
- `limit` (查询参数): 数据条数限制，默认10

**示例**: `GET /perception/entity/entity_001/fused?limit=3`

**响应**:
```json
{
  "success": true,
  "message": "获取实体融合数据成功",
  "data": [
    {
      "id": "fused_001",
      "entityId": "entity_001",
      "timestamp": 1640995200000,
      "confidence": 0.82,
      "worldModel": {
        "entities": {},
        "environment": {},
        "social": {},
        "temporal": {}
      },
      "attentionFocus": [],
      "predictions": [],
      "sourceDataIds": ["perception_001", "perception_002"]
    }
  ]
}
```

### 5. 获取处理统计信息

**接口**: `GET /perception/statistics`

**描述**: 获取感知数据处理的统计信息

**响应**:
```json
{
  "success": true,
  "message": "获取统计信息成功",
  "data": {
    "totalPerceptions": 1250,
    "modalityBreakdown": {
      "VISUAL": 500,
      "AUDITORY": 300,
      "SOCIAL": 250,
      "ENVIRONMENTAL": 200
    },
    "averageConfidence": 0.78,
    "anomalyRate": 0.05,
    "processingLatency": 125.5,
    "fusionRate": 0.85,
    "dataQuality": 0.82
  }
}
```

### 6. 更新处理配置

**接口**: `PUT /perception/config`

**描述**: 动态更新感知数据处理配置

**请求体**:
```json
{
  "enableRealTimeProcessing": true,
  "batchSize": 150,
  "fusionThreshold": 0.8,
  "anomalyThreshold": 0.25,
  "qualityThreshold": 0.7,
  "enablePrediction": true,
  "predictionHorizon": 6000
}
```

**响应**:
```json
{
  "success": true,
  "message": "配置更新成功",
  "data": {
    "enableRealTimeProcessing": true,
    "batchSize": 150,
    "fusionThreshold": 0.8,
    "anomalyThreshold": 0.25,
    "qualityThreshold": 0.7,
    "enablePrediction": true,
    "predictionHorizon": 6000
  }
}
```

## 健康检查接口

### 1. 基础健康检查

**接口**: `GET /health`

**响应**:
```json
{
  "success": true,
  "message": "服务健康",
  "data": {
    "healthy": true,
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "version": "1.0.0",
    "uptime": 3600,
    "memory": {
      "used": 134217728,
      "total": **********,
      "percentage": 1.56
    }
  }
}
```

### 2. 详细健康检查

**接口**: `GET /health/detailed`

**响应**:
```json
{
  "success": true,
  "message": "服务健康",
  "data": {
    "healthy": true,
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "version": "1.0.0",
    "uptime": 3600,
    "memory": {
      "used": 134217728,
      "total": **********,
      "percentage": 1.56
    },
    "cpu": {
      "usage": 0.15,
      "loadAverage": [0.5, 0.3, 0.2]
    },
    "dependencies": {
      "redis": {
        "status": "healthy",
        "latency": 2
      },
      "engine": {
        "status": "healthy"
      }
    },
    "metrics": {
      "totalRequests": 1000,
      "successfulRequests": 980,
      "failedRequests": 20,
      "averageResponseTime": 125.5,
      "queueSize": 5,
      "processingRate": 50.2
    }
  }
}
```

### 3. 就绪检查

**接口**: `GET /health/ready`

**响应**:
```json
{
  "success": true,
  "message": "服务就绪",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 4. 存活检查

**接口**: `GET /health/live`

**响应**:
```json
{
  "success": true,
  "message": "服务存活",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 感知模态说明

### VISUAL (视觉感知)
- 对象检测和识别
- 场景理解和分析
- 光照条件评估
- 空间关系分析

### AUDITORY (听觉感知)
- 声音识别和分类
- 环境音分析
- 语音处理
- 音频特征提取

### SOCIAL (社交感知)
- 实体交互分析
- 群体行为识别
- 社交关系建模
- 交互模式识别

### ENVIRONMENTAL (环境感知)
- 天气状况监测
- 障碍物检测
- 资源分布分析
- 环境变化跟踪

## 错误代码

| 状态码 | 描述 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用 |

## 使用示例

### cURL示例

```bash
# 处理感知数据
curl -X POST http://localhost:3050/api/v1/perception/process \
  -H "Content-Type: application/json" \
  -d '{
    "id": "perception_001",
    "entityId": "entity_001",
    "sessionId": "session_001",
    "modality": "VISUAL",
    "timestamp": 1640995200000,
    "confidence": 0.85,
    "data": {
      "objects": [{"id": "obj_001", "type": "person"}]
    }
  }'

# 获取统计信息
curl -X GET http://localhost:3050/api/v1/perception/statistics

# 健康检查
curl -X GET http://localhost:3050/api/v1/health
```

### JavaScript示例

```javascript
// 处理感知数据
const response = await fetch('http://localhost:3050/api/v1/perception/process', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    id: 'perception_001',
    entityId: 'entity_001',
    sessionId: 'session_001',
    modality: 'VISUAL',
    timestamp: Date.now(),
    confidence: 0.85,
    data: {
      objects: [{ id: 'obj_001', type: 'person' }]
    }
  })
});

const result = await response.json();
console.log(result);
```

## 注意事项

1. **时间戳格式**: 使用Unix时间戳（毫秒）
2. **置信度范围**: 0.0 到 1.0 之间
3. **数据大小限制**: 单个请求最大10MB
4. **并发限制**: 默认最大50个并发请求
5. **超时设置**: 请求超时时间为30秒
