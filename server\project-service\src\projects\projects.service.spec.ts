/**
 * 项目服务单元测试
 */
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException, ForbiddenException } from '@nestjs/common';
import { ProjectsService } from './projects.service';
import { Project, ProjectVisibility } from './entities/project.entity';
import { ProjectMember, ProjectMemberRole } from './entities/project-member.entity';
import { ProjectSetting } from './entities/project-setting.entity';

describe('ProjectsService', () => {
  let service: ProjectsService;
  let projectRepository: Repository<Project>;
  let memberRepository: Repository<ProjectMember>;
  let settingRepository: Repository<ProjectSetting>;
  let userService: any;

  const mockProject = {
    id: 'project-1',
    name: '测试项目',
    description: '这是一个测试项目',
    ownerId: 'user-1',
    visibility: ProjectVisibility.PRIVATE,
    isTemplate: false,
    isArchived: false,
    members: [],
    settings: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockUser = {
    id: 'user-1',
    username: 'testuser',
    email: '<EMAIL>',
    isActive: true,
  };

  beforeEach(async () => {
    const mockUserService = {
      send: jest.fn().mockReturnValue({
        toPromise: () => Promise.resolve(mockUser),
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProjectsService,
        {
          provide: getRepositoryToken(Project),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(ProjectMember),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(ProjectSetting),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: 'USER_SERVICE',
          useValue: mockUserService,
        },
      ],
    }).compile();

    service = module.get<ProjectsService>(ProjectsService);
    projectRepository = module.get<Repository<Project>>(getRepositoryToken(Project));
    memberRepository = module.get<Repository<ProjectMember>>(getRepositoryToken(ProjectMember));
    settingRepository = module.get<Repository<ProjectSetting>>(getRepositoryToken(ProjectSetting));
    userService = module.get('USER_SERVICE');
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a project successfully', async () => {
      const createProjectDto = {
        name: '新项目',
        description: '项目描述',
        visibility: ProjectVisibility.PRIVATE,
      };

      jest.spyOn(projectRepository, 'create').mockReturnValue(mockProject as any);
      jest.spyOn(projectRepository, 'save').mockResolvedValue(mockProject as any);
      jest.spyOn(memberRepository, 'create').mockReturnValue({} as any);
      jest.spyOn(memberRepository, 'save').mockResolvedValue({} as any);
      jest.spyOn(service, 'findOne').mockResolvedValue(mockProject as any);

      const result = await service.create('user-1', createProjectDto);

      expect(result).toEqual(mockProject);
      expect(projectRepository.create).toHaveBeenCalledWith({
        ...createProjectDto,
        ownerId: 'user-1',
      });
    });
  });

  describe('findOne', () => {
    it('should return a project if found', async () => {
      jest.spyOn(projectRepository, 'findOne').mockResolvedValue(mockProject as any);

      const result = await service.findOne('project-1');

      expect(result).toEqual(mockProject);
      expect(projectRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'project-1' },
        relations: ['members', 'settings'],
      });
    });

    it('should throw NotFoundException if project not found', async () => {
      jest.spyOn(projectRepository, 'findOne').mockResolvedValue(null);

      await expect(service.findOne('non-existent')).rejects.toThrow(NotFoundException);
    });
  });
});
