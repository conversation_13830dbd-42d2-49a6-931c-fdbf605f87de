/**
 * 感知数据处理服务主模块
 */

import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';

// 控制器
import { PerceptionController } from './controllers/perception.controller';
import { HealthController } from './controllers/health.controller';

// 服务
import { PerceptionProcessingService } from './services/perception-processing.service';
import { HealthService } from './services/health.service';

// 提供者
import { RedisProvider } from './providers/redis.provider';
import { ConfigProvider } from './providers/config.provider';

/**
 * 感知数据处理服务主模块
 */
@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
      expandVariables: true,
      validationOptions: {
        allowUnknown: false,
        abortEarly: true,
      },
    }),

    // 事件发射器模块
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 20,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),

    // 定时任务模块
    ScheduleModule.forRoot(),
  ],

  controllers: [
    PerceptionController,
    HealthController,
  ],

  providers: [
    PerceptionProcessingService,
    HealthService,
    ConfigProvider,
    {
      provide: 'REDIS_CONFIG',
      useFactory: (configService: ConfigService) => ({
        host: configService.get<string>('REDIS_HOST', 'localhost'),
        port: configService.get<number>('REDIS_PORT', 6379),
        password: configService.get<string>('REDIS_PASSWORD'),
        db: configService.get<number>('REDIS_DB', 0),
        retryDelayOnFailover: 100,
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
      }),
      inject: [ConfigService],
    },
    {
      provide: 'REDIS_CLIENT',
      useFactory: () => {
        // 返回一个模拟的Redis客户端用于开发
        return {
          ping: () => Promise.resolve('PONG'),
          setex: () => Promise.resolve('OK'),
          lpush: () => Promise.resolve(1),
          ltrim: () => Promise.resolve('OK'),
          lrange: () => Promise.resolve([]),
          get: () => Promise.resolve(null),
          subscribe: () => {},
          on: () => {},
          disconnect: () => {},
        };
      },
    },
  ],

  exports: [
    PerceptionProcessingService,
    HealthService,
  ],
})
export class AppModule {}
