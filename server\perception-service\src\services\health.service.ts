/**
 * 健康检查服务
 */

import { Injectable, Logger, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
import * as os from 'os';
import * as process from 'process';

import { HealthStatusDto } from '../dto/health.dto';

/**
 * 健康检查服务
 */
@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);
  private readonly startTime = Date.now();
  private metrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    totalResponseTime: 0,
    queueSize: 0,
    processingRate: 0,
  };

  constructor(
    private readonly configService: ConfigService,
    @Inject('REDIS_CLIENT') private readonly redis: Redis,
  ) {}

  /**
   * 基础健康检查
   */
  async checkHealth(): Promise<HealthStatusDto> {
    try {
      const uptime = Math.floor((Date.now() - this.startTime) / 1000);
      const memoryUsage = process.memoryUsage();
      const totalMemory = os.totalmem();
      const usedMemory = memoryUsage.heapUsed;

      return {
        healthy: true,
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: this.configService.get<string>('npm_package_version', '1.0.0'),
        uptime,
        memory: {
          used: usedMemory,
          total: totalMemory,
          percentage: (usedMemory / totalMemory) * 100,
        },
      };
    } catch (error) {
      this.logger.error('健康检查失败:', error);
      return {
        healthy: false,
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        version: this.configService.get<string>('npm_package_version', '1.0.0'),
        uptime: Math.floor((Date.now() - this.startTime) / 1000),
        errors: [error.message],
      };
    }
  }

  /**
   * 详细健康检查
   */
  async checkDetailedHealth(): Promise<HealthStatusDto> {
    try {
      const basicHealth = await this.checkHealth();
      const dependencies = await this.checkDependencies();
      const cpuUsage = process.cpuUsage();
      const loadAverage = os.loadavg();

      return {
        ...basicHealth,
        cpu: {
          usage: (cpuUsage.user + cpuUsage.system) / 1000000, // 转换为秒
          loadAverage,
        },
        dependencies,
        metrics: {
          totalRequests: this.metrics.totalRequests,
          successfulRequests: this.metrics.successfulRequests,
          failedRequests: this.metrics.failedRequests,
          averageResponseTime: this.metrics.totalRequests > 0 
            ? this.metrics.totalResponseTime / this.metrics.totalRequests 
            : 0,
          queueSize: this.metrics.queueSize,
          processingRate: this.metrics.processingRate,
        },
      };
    } catch (error) {
      this.logger.error('详细健康检查失败:', error);
      const basicHealth = await this.checkHealth();
      return {
        ...basicHealth,
        healthy: false,
        status: 'unhealthy',
        errors: [...(basicHealth.errors || []), error.message],
      };
    }
  }

  /**
   * 检查依赖服务
   */
  private async checkDependencies(): Promise<any> {
    const dependencies = {
      redis: await this.checkRedis(),
      engine: await this.checkEngine(),
    };

    return dependencies;
  }

  /**
   * 检查Redis连接
   */
  private async checkRedis(): Promise<any> {
    try {
      const start = Date.now();
      await this.redis.ping();
      const latency = Date.now() - start;

      return {
        status: 'healthy',
        latency,
      };
    } catch (error) {
      this.logger.error('Redis健康检查失败:', error);
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  /**
   * 检查引擎连接
   */
  private async checkEngine(): Promise<any> {
    try {
      // 这里可以添加对引擎的健康检查
      // 目前返回健康状态
      return {
        status: 'healthy',
      };
    } catch (error) {
      this.logger.error('引擎健康检查失败:', error);
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  /**
   * 就绪检查
   */
  async checkReadiness(): Promise<boolean> {
    try {
      // 检查Redis连接
      await this.redis.ping();
      
      // 检查其他必要的依赖
      // ...

      return true;
    } catch (error) {
      this.logger.error('就绪检查失败:', error);
      return false;
    }
  }

  /**
   * 存活检查
   */
  async checkLiveness(): Promise<boolean> {
    try {
      // 基本的存活检查
      const memoryUsage = process.memoryUsage();
      const maxMemory = 1024 * 1024 * 1024; // 1GB

      // 检查内存使用是否过高
      if (memoryUsage.heapUsed > maxMemory) {
        this.logger.warn('内存使用过高');
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error('存活检查失败:', error);
      return false;
    }
  }

  /**
   * 记录请求指标
   */
  recordRequest(success: boolean, responseTime: number): void {
    this.metrics.totalRequests++;
    this.metrics.totalResponseTime += responseTime;

    if (success) {
      this.metrics.successfulRequests++;
    } else {
      this.metrics.failedRequests++;
    }
  }

  /**
   * 更新队列大小
   */
  updateQueueSize(size: number): void {
    this.metrics.queueSize = size;
  }

  /**
   * 更新处理速率
   */
  updateProcessingRate(rate: number): void {
    this.metrics.processingRate = rate;
  }

  /**
   * 获取指标
   */
  getMetrics(): any {
    return { ...this.metrics };
  }

  /**
   * 重置指标
   */
  resetMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalResponseTime: 0,
      queueSize: 0,
      processingRate: 0,
    };
  }
}
